#include "databaseapi.h"
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include <QSqlQuery>
#include <QSqlError>
#include <QDateTime>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJSEngine>
#include <QJSValue>
#include <QLoggingCategory>
#include <QMetaMethod>
#include <QRegularExpression>
#include <iostream>
#include <QFile>
#include <QFileInfo>
#include <QThread>
#include <QMutexLocker>
#include <QMap>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QImageReader>
#include <QSize>
#include <QCryptographicHash>
#include <QDate>
#include <QNetworkRequest>
#include <QEventLoop>
#include <QTimer>
#include <QUrl>

// 定义日志类别
Q_DECLARE_LOGGING_CATEGORY(databaseApi)
Q_LOGGING_CATEGORY(databaseApi, "inkcop.database")

// 静态成员初始化
QMutex DatabaseApi::s_snowflakeMutex;
quint64 DatabaseApi::s_lastTimestamp = 0;
quint64 DatabaseApi::s_sequence = 0;

DatabaseApi::DatabaseApi(QObject *parent)
    : QObject(parent), m_networkManager(new QNetworkAccessManager(this))
{
    // 配置 Qt 调试输出
    qSetMessagePattern("[%{time yyyy-MM-dd hh:mm:ss.zzz}] %{type} %{category}: %{message}");

    // 启用所有级别的日志
    QLoggingCategory::setFilterRules(QStringLiteral("inkcop.database.debug=true\n"
                                                    "inkcop.database.info=true\n"
                                                    "inkcop.database.warning=true\n"
                                                    "inkcop.database.critical=true"));

    // 自定义消息处理器
    qInstallMessageHandler([](QtMsgType type, const QMessageLogContext &context, const QString &msg)
                           {
        QString txt;
        switch (type) {
        case QtDebugMsg:
            txt = QString("[Debug] %1").arg(msg);
            break;
        case QtWarningMsg:
            txt = QString("[Warning] %1").arg(msg);
            break;
        case QtCriticalMsg:
            txt = QString("[Critical] %1").arg(msg);
            break;
        case QtFatalMsg:
            txt = QString("[Fatal] %1").arg(msg);
            break;
        case QtInfoMsg:
            txt = QString("[Info] %1").arg(msg);
            break;
        }
        
        // 只输出到 stdout，避免重复输出
        std::cout << qPrintable(txt) << std::endl;
        fflush(stdout); });

    initDatabase();

    // 调试：检查方法是否正确注册
    qDebug() << "🎯 [DatabaseApi] Constructor completed. Checking method registration:";
    const QMetaObject *metaObj = this->metaObject();
    for (int i = 0; i < metaObj->methodCount(); ++i)
    {
        QMetaMethod method = metaObj->method(i);
        QString methodName = method.name();
        if (methodName.contains("reorder"))
        {
            qDebug() << "🎯 [DatabaseApi] Found method:" << methodName
                     << "parameterCount:" << method.parameterCount()
                     << "signature:" << method.methodSignature();
        }
    }
}

DatabaseApi::~DatabaseApi()
{
    if (m_db.isOpen())
    {
        // 确保所有更改都已写入
        QSqlQuery query(m_db);
        query.exec("PRAGMA wal_checkpoint(TRUNCATE)");
        m_db.close();
    }
}

bool DatabaseApi::initDatabase()
{
    // 获取应用数据目录
    QString dataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    if (dataPath.isEmpty())
    {
        qDebug() << "Error: Failed to get application data location";
        return false;
    }

    // 创建用户数据目录结构 - 使用QDir确保跨平台路径兼容性
    QString userPath = QDir(dataPath).filePath("user");
    QString dbPath = QDir(userPath).filePath("db");
    QString imagesPath = QDir(userPath).filePath("images");

    // Windows路径信息
    qDebug() << "Data Path:" << dataPath;
    qDebug() << "User Path:" << userPath;
    qDebug() << "Database Path:" << dbPath;
    qDebug() << "Images Path:" << imagesPath;

    // 确保目录存在
    if (!ensureDirectoryExists(dbPath))
    {
        qDebug() << "Error: Failed to create database directory:" << dbPath;
        return false;
    }

    if (!ensureDirectoryExists(imagesPath))
    {
        qDebug() << "Error: Failed to create images directory:" << imagesPath;
        return false;
    }

    // 打开数据库连接
    m_db = QSqlDatabase::addDatabase("QSQLITE");
    QString dbFilePath = QDir(dbPath).filePath("inkcop.db");
    // 确保数据库文件路径使用本地分隔符
    dbFilePath = QDir::toNativeSeparators(dbFilePath);
    m_db.setDatabaseName(dbFilePath);

    qDebug() << "Database File Path:" << dbFilePath;

    // 设置数据库连接参数，优化并发性能
    QSqlQuery query;
    if (!m_db.open())
    {
        qDebug() << "Error: Failed to open database:" << m_db.lastError().text();
        return false;
    }

    // 设置数据库连接参数
    query = QSqlQuery(m_db);

    // 启用外键约束
    if (!query.exec("PRAGMA foreign_keys = ON"))
    {
        qDebug() << "Error: Failed to enable foreign keys:" << query.lastError().text();
        return false;
    }

    // 设置 WAL 模式，提高并发性能
    if (!query.exec("PRAGMA journal_mode = WAL"))
    {
        qDebug() << "Error: Failed to set WAL mode:" << query.lastError().text();
        return false;
    }

    // 设置同步模式为 NORMAL，在性能和安全性之间取得平衡
    if (!query.exec("PRAGMA synchronous = NORMAL"))
    {
        qDebug() << "Error: Failed to set synchronous mode:" << query.lastError().text();
        return false;
    }

    // 设置缓存大小，提高性能（单位：KB）
    if (!query.exec("PRAGMA cache_size = -2000"))
    { // 使用 2MB 缓存
        qDebug() << "Error: Failed to set cache size:" << query.lastError().text();
        return false;
    }

    // 设置临时存储模式为内存，提高性能
    if (!query.exec("PRAGMA temp_store = MEMORY"))
    {
        qDebug() << "Error: Failed to set temp store mode:" << query.lastError().text();
        return false;
    }

    // 设置页面大小，优化性能
    if (!query.exec("PRAGMA page_size = 4096"))
    {
        qDebug() << "Error: Failed to set page size:" << query.lastError().text();
        return false;
    }

    // 设置锁定超时时间（毫秒）
    if (!query.exec("PRAGMA busy_timeout = 5000"))
    {
        qDebug() << "Error: Failed to set busy timeout:" << query.lastError().text();
        return false;
    }

    return createTables();
}

bool DatabaseApi::createTables()
{
    QSqlQuery query;

    // 创建文件夹表，增加 parent_id 字段
    if (!query.exec("CREATE TABLE IF NOT EXISTS folders ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                    "name TEXT NOT NULL,"
                    "parent_id INTEGER,"
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP,"
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,"
                    "FOREIGN KEY (parent_id) REFERENCES folders(id))"))
    {
        qDebug() << "Error: Failed to create folders table:" << query.lastError().text();
        return false;
    }

    // 创建文件夹-文件夹关系表（仅用于排序）
    // parent_id = -1 表示根文件夹，不需要外键约束
    if (!query.exec("CREATE TABLE IF NOT EXISTS folder_folder_rel ("
                    "parent_id INTEGER,"
                    "child_id INTEGER NOT NULL,"
                    "sort_order INTEGER NOT NULL,"
                    "PRIMARY KEY (child_id),"
                    "FOREIGN KEY (child_id) REFERENCES folders(id),"
                    "CHECK (parent_id = -1 OR (parent_id > 0 AND parent_id != child_id)))"))
    {
        qDebug() << "Error: Failed to create folder_folder_rel table:" << query.lastError().text();
        return false;
    }

    // 创建文档表（移除知识库相关字段，改用关联表）
    if (!query.exec("CREATE TABLE IF NOT EXISTS documents ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                    "title TEXT NOT NULL,"
                    "content JSON,"
                    "metadata JSON,"
                    "snapshot JSON,"
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP,"
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP)"))
    {
        qDebug() << "Error: Failed to create documents table:" << query.lastError().text();
        return false;
    }

    // 创建文档-知识库关联表
    if (!query.exec("CREATE TABLE IF NOT EXISTS document_knowledge_associations ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                    "document_id INTEGER NOT NULL,"
                    "knowledge_document_id INTEGER NOT NULL,"
                    "knowledge_base_id INTEGER NOT NULL,"
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP,"
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,"
                    "FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,"
                    "UNIQUE(document_id, knowledge_base_id))"))
    {
        qDebug() << "Error: Failed to create document_knowledge_associations table:" << query.lastError().text();
        return false;
    }

    // 创建对话表（可选关联document_id，支持独立存在）
    if (!query.exec("CREATE TABLE IF NOT EXISTS conversations ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                    "document_id INTEGER,"
                    "title TEXT NOT NULL,"
                    "messages JSON NOT NULL,"
                    "prompt TEXT,"
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP,"
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,"
                    "FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE)"))
    {
        qDebug() << "Error: Failed to create conversations table:" << query.lastError().text();
        return false;
    }

    // 创建文件夹-文档关系表
    if (!query.exec("CREATE TABLE IF NOT EXISTS folder_document_rel ("
                    "folder_id INTEGER NOT NULL,"
                    "document_id INTEGER NOT NULL,"
                    "sort_order INTEGER NOT NULL,"
                    "PRIMARY KEY (folder_id, document_id),"
                    "FOREIGN KEY (folder_id) REFERENCES folders(id),"
                    "FOREIGN KEY (document_id) REFERENCES documents(id))"))
    {
        qDebug() << "Error: Failed to create folder_document_rel table:" << query.lastError().text();
        return false;
    }

    // 创建文档-文档关系表
    if (!query.exec("CREATE TABLE IF NOT EXISTS document_document_rel ("
                    "from_doc_id INTEGER NOT NULL,"
                    "to_doc_id INTEGER NOT NULL,"
                    "sort_order INTEGER NOT NULL,"
                    "PRIMARY KEY (from_doc_id, to_doc_id),"
                    "FOREIGN KEY (from_doc_id) REFERENCES documents(id),"
                    "FOREIGN KEY (to_doc_id) REFERENCES documents(id),"
                    "CHECK (from_doc_id != to_doc_id))"))
    {
        qDebug() << "Error: Failed to create document_document_rel table:" << query.lastError().text();
        return false;
    }

    // 创建设置表
    if (!query.exec("CREATE TABLE IF NOT EXISTS settings ("
                    "key TEXT PRIMARY KEY,"
                    "value TEXT,"
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP)"))
    {
        qDebug() << "Error: Failed to create settings table:" << query.lastError().text();
        return false;
    }

    // 创建图片表 - 重构版本：移除document_id，改用引用表管理
    if (!query.exec("CREATE TABLE IF NOT EXISTS images ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                    "file_path TEXT NOT NULL UNIQUE,"
                    "original_url TEXT,"
                    "mime_type TEXT NOT NULL,"
                    "file_size INTEGER DEFAULT 0,"
                    "width INTEGER DEFAULT 0,"
                    "height INTEGER DEFAULT 0,"
                    "hash TEXT,"
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP,"
                    "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP)"))
    {
        qDebug() << "Error: Failed to create images table:" << query.lastError().text();
        return false;
    }

    // 创建图片引用表 - 跟踪哪些文档引用了哪些图片
    if (!query.exec("CREATE TABLE IF NOT EXISTS image_references ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                    "image_id INTEGER NOT NULL,"
                    "document_id INTEGER NOT NULL,"
                    "created_at DATETIME DEFAULT CURRENT_TIMESTAMP,"
                    "FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,"
                    "FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,"
                    "UNIQUE(image_id, document_id))"))
    {
        qDebug() << "Error: Failed to create image_references table:" << query.lastError().text();
        return false;
    }

    return migrateDatabase();
}

bool DatabaseApi::migrateDatabase()
{
    QSqlQuery query;

    // 检查documents表的列信息
    if (!query.exec("PRAGMA table_info(documents)"))
    {
        qDebug() << "Error: Failed to get table info:" << query.lastError().text();
        return false;
    }

    bool hasFolderId = false;

    while (query.next())
    {
        QString columnName = query.value("name").toString();
        if (columnName == "folder_id")
        {
            hasFolderId = true;
        }
    }

    // 添加 folder_id 列（如果不存在）
    if (!hasFolderId)
    {
        if (!query.exec("ALTER TABLE documents ADD COLUMN folder_id INTEGER REFERENCES folders(id)"))
        {
            qDebug() << "Error: Failed to add folder_id column:" << query.lastError().text();
            return false;
        }
    }

    return true;
}

// 用于更严谨地判断数据库字段是否为 null
inline QJsonValue toJsonIntOrNull(const QVariant &v)
{
    if (!v.isValid() || v.isNull() || (v.typeId() == QMetaType::QString && v.toString().isEmpty()))
    {
        return QJsonValue::Null;
    }
    else
    {
        return v.toInt();
    }
}

// 专门用于ID字段，确保永远不返回null（对于主键ID）
inline QJsonValue toJsonIdSafe(const QVariant &v)
{
    if (!v.isValid() || v.isNull() || (v.typeId() == QMetaType::QString && v.toString().isEmpty()))
    {
        qWarning() << "Invalid ID value detected:" << v << "- this should not happen for primary keys";
        return 0; // 返回0而不是null，避免前端错误
    }
    else
    {
        return v.toInt();
    }
}

// 专门用于可为null的外键ID字段，统一使用-1表示null
// 适用于: conversations.document_id, documents.folder_id, folders.parent_id
inline QJsonValue toJsonNullableIdSafe(const QVariant &v)
{
    if (!v.isValid() || v.isNull() || (v.typeId() == QMetaType::QString && v.toString().isEmpty()))
    {
        return -1; // 使用-1表示null，避免前端处理null值的复杂性
    }
    else
    {
        return v.toInt();
    }
}

QJsonObject DatabaseApi::createDocument(const QString &title, const QString &content, int folder_id, const QString &metadata)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 验证不允许在根目录下创建文档
        if (folder_id == -1)
        {
            throw QSqlError("Root folder not allowed", "Documents cannot be created in the root directory. Please select a folder.");
        }

        // 验证 content 是否为有效的 JSON
        QJsonDocument doc = QJsonDocument::fromJson(content.toUtf8());
        if (doc.isNull())
        {
            throw QSqlError("Invalid JSON", "Content is not a valid JSON");
        }

        // 检查 folder_id 是否有效
        query.prepare("SELECT COUNT(*) FROM folders WHERE id = ?");
        query.addBindValue(folder_id);
        if (!query.exec() || !query.next() || query.value(0).toInt() == 0)
        {
            throw QSqlError("Invalid folder_id", "folder_id does not exist");
        }

        // 创建文档，content 和 metadata 直接存储
        query.prepare("INSERT INTO documents (title, content, metadata) VALUES (?, ?, ?)");
        query.addBindValue(title);
        query.addBindValue(content);
        query.addBindValue(metadata);

        if (!query.exec())
        {
            throw query.lastError();
        }

        int docId = query.lastInsertId().toInt();

        // 2. 创建文件夹-文档关系
        query.prepare("SELECT MAX(sort_order) FROM folder_document_rel WHERE folder_id = ?");
        query.addBindValue(folder_id);
        if (!query.exec() || !query.next())
        {
            throw query.lastError();
        }
        int maxSortOrder = query.value(0).toInt();
        int newSortOrder = maxSortOrder + 1;

        query.prepare("INSERT INTO folder_document_rel (folder_id, document_id, sort_order) VALUES (?, ?, ?)");
        query.addBindValue(folder_id);
        query.addBindValue(docId);
        query.addBindValue(newSortOrder);
        if (!query.exec())
        {
            throw query.lastError();
        }

        m_db.commit();
        result["success"] = true;
        result["id"] = docId;
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "Error: Failed to create document:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::getDocument(int id)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    query.prepare("SELECT d.*, "
                  "(SELECT json_group_array(json_object("
                  "  'id', c.id, "
                  "  'title', c.title, "
                  "  'messages', c.messages, "
                  "  'prompt', c.prompt, "
                  "  'created_at', c.created_at, "
                  "  'updated_at', c.updated_at)) "
                  "FROM conversations c "
                  "WHERE c.document_id = d.id "
                  "ORDER BY c.created_at DESC) as conversations_json "
                  "FROM documents d WHERE d.id = ?");
    query.addBindValue(id);

    if (query.exec() && query.next())
    {
        result["success"] = true;
        result["id"] = toJsonIdSafe(query.value("id"));
        result["title"] = query.value("title").toString();
        result["content"] = query.value("content").toString();
        result["metadata"] = query.value("metadata").toString();
        result["created_at"] = query.value("created_at").toString();
        result["updated_at"] = query.value("updated_at").toString();

        // 解析快照 JSON
        QString snapshotJson = query.value("snapshot").toString();
        if (!snapshotJson.isEmpty() && snapshotJson != "null")
        {
            QJsonDocument doc = QJsonDocument::fromJson(snapshotJson.toUtf8());
            if (!doc.isNull() && doc.isArray())
            {
                result["snapshot"] = doc.array();
            }
            else
            {
                result["snapshot"] = QJsonArray();
            }
        }
        else
        {
            result["snapshot"] = QJsonArray();
        }

        // 解析对话 JSON
        QString conversationsJson = query.value("conversations_json").toString();
        if (!conversationsJson.isEmpty() && conversationsJson != "null")
        {
            QJsonDocument doc = QJsonDocument::fromJson(conversationsJson.toUtf8());
            result["conversations"] = doc.array();
        }
        else
        {
            result["conversations"] = QJsonArray();
        }

        // 获取所属文件夹ID
        query.prepare("SELECT folder_id FROM folder_document_rel WHERE document_id = ?");
        query.addBindValue(id);
        if (query.exec() && query.next())
        {
            result["folder_id"] = toJsonNullableIdSafe(query.value("folder_id"));
        }
        else
        {
            result["folder_id"] = -1; // 使用-1表示文档不在任何文件夹中
        }
    }
    else
    {
        result["success"] = false;
        result["error"] = "document not found";
    }

    return result;
}

QJsonObject DatabaseApi::updateDocument(int id, const QString &title, const QString &content, int folder_id, const QString &metadata)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 验证 content 是否为有效的 JSON
        QJsonDocument doc = QJsonDocument::fromJson(content.toUtf8());
        if (doc.isNull())
        {
            throw QSqlError("Invalid JSON", "Content is not a valid JSON");
        }

        // 获取更新前的时间戳用于调试
        QSqlQuery timeQuery(m_db);
        timeQuery.prepare("SELECT updated_at FROM documents WHERE id = ?");
        timeQuery.addBindValue(id);
        QString beforeTime = "";
        if (timeQuery.exec() && timeQuery.next())
        {
            beforeTime = timeQuery.value("updated_at").toString();
        }

        qDebug() << "🔄 [DatabaseApi] 更新文档 ID:" << id << "标题:" << title << "更新前时间:" << beforeTime;

        // 更新文档内容
        query.prepare("UPDATE documents SET title = ?, content = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        query.addBindValue(title);
        query.addBindValue(content);
        query.addBindValue(metadata);
        query.addBindValue(id);

        if (!query.exec())
        {
            throw query.lastError();
        }

        // 检查更新后的时间戳
        timeQuery.prepare("SELECT updated_at FROM documents WHERE id = ?");
        timeQuery.addBindValue(id);
        QString afterTime = "";
        if (timeQuery.exec() && timeQuery.next())
        {
            afterTime = timeQuery.value("updated_at").toString();
        }

        qDebug() << "✅ [DatabaseApi] 文档更新完成 ID:" << id << "更新后时间:" << afterTime << "时间是否变化:" << (beforeTime != afterTime);

        // 处理文件夹关系
        // 2.1 获取当前文件夹ID
        query.prepare("SELECT folder_id FROM folder_document_rel WHERE document_id = ?");
        query.addBindValue(id);
        if (!query.exec())
        {
            throw query.lastError();
        }

        int currentFolderId = -1;
        if (query.next())
        {
            currentFolderId = query.value("folder_id").toInt();
        }

        // 2.2 如果文件夹发生变化
        if (currentFolderId != folder_id)
        {
            // 删除旧关系
            if (currentFolderId > 0)
            {
                query.prepare("DELETE FROM folder_document_rel WHERE document_id = ?");
                query.addBindValue(id);
                if (!query.exec())
                {
                    throw query.lastError();
                }
            }

            // 创建新关系
            if (folder_id > 0)
            {
                // 获取新文件夹下的最大排序值
                query.prepare("SELECT MAX(sort_order) FROM folder_document_rel WHERE folder_id = ?");
                query.addBindValue(folder_id);
                if (!query.exec() || !query.next())
                {
                    throw query.lastError();
                }

                int maxSortOrder = query.value(0).toInt();
                int newSortOrder = maxSortOrder + 1;

                query.prepare("INSERT INTO folder_document_rel (folder_id, document_id, sort_order) VALUES (?, ?, ?)");
                query.addBindValue(folder_id);
                query.addBindValue(id);
                query.addBindValue(newSortOrder);

                if (!query.exec())
                {
                    throw query.lastError();
                }
            }
        }

        m_db.commit();
        result["success"] = true;
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "Error: Failed to update document:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

// 新增：专门用于移动文档的方法
QJsonObject DatabaseApi::moveDocument(int documentId, int targetFolderId)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        qDebug() << "📁 [DatabaseApi] 移动文档 ID:" << documentId << "到文件夹:" << targetFolderId;

        // 1. 验证不允许移动文档到根目录
        if (targetFolderId == -1)
        {
            throw QSqlError("Root folder not allowed", "Documents cannot be moved to the root directory. Please select a folder.");
        }

        // 2. 检查文档是否存在
        query.prepare("SELECT id FROM documents WHERE id = ?");
        query.addBindValue(documentId);
        if (!query.exec() || !query.next())
        {
            throw QSqlError("Document not found", "Document does not exist");
        }

        // 3. 获取当前文档的文件夹ID
        query.prepare("SELECT folder_id FROM folder_document_rel WHERE document_id = ?");
        query.addBindValue(documentId);
        if (!query.exec())
        {
            throw query.lastError();
        }

        int currentFolderId = -1;
        if (query.next())
        {
            currentFolderId = query.value("folder_id").toInt();
        }

        // 4. 如果目标文件夹与当前文件夹相同，直接返回成功
        if (currentFolderId == targetFolderId)
        {
            qDebug() << "📁 [DatabaseApi] 文档已在目标文件夹中，无需移动";
            result["success"] = true;
            result["message"] = "文档已在目标文件夹中";
            m_db.commit();
            return result;
        }

        // 5. 检查目标文件夹是否存在（由于已经验证不是根目录，这里直接检查）
        query.prepare("SELECT id FROM folders WHERE id = ?");
        query.addBindValue(targetFolderId);
        if (!query.exec() || !query.next())
        {
            throw QSqlError("Target folder not found", "Target folder does not exist");
        }

        // 6. 删除旧的文件夹关系
        if (currentFolderId != -1)
        {
            query.prepare("DELETE FROM folder_document_rel WHERE document_id = ?");
            query.addBindValue(documentId);
            if (!query.exec())
            {
                throw query.lastError();
            }
        }

        // 7. 创建新的文件夹关系（由于已经验证目标不是根目录，直接创建关系）
        // 7.1 将目标文件夹中现有文档的sort_order都+1，为新文档腾出第一位
        query.prepare("UPDATE folder_document_rel SET sort_order = sort_order + 1 WHERE folder_id = ?");
        query.addBindValue(targetFolderId);
        if (!query.exec())
        {
            throw query.lastError();
        }

        // 7.2 插入新的文件夹关系，sort_order设为0（排在第一位）
        query.prepare("INSERT INTO folder_document_rel (folder_id, document_id, sort_order) VALUES (?, ?, 0)");
        query.addBindValue(targetFolderId);
        query.addBindValue(documentId);
        if (!query.exec())
        {
            throw query.lastError();
        }

        m_db.commit();
        result["success"] = true;
        result["message"] = QString("文档已成功移动到%1").arg(targetFolderId == -1 ? "根目录" : QString("文件夹%1").arg(targetFolderId));
        qDebug() << "✅ [DatabaseApi] 文档移动完成";
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "❌ [DatabaseApi] 移动文档失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

// 新增：专门用于移动文件夹的方法
QJsonObject DatabaseApi::moveFolder(int folderId, int targetFolderId)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        qDebug() << "📁 [DatabaseApi] 移动文件夹 ID:" << folderId << "到文件夹:" << targetFolderId;

        // 1. 检查源文件夹是否存在
        query.prepare("SELECT id, parent_id FROM folders WHERE id = ?");
        query.addBindValue(folderId);
        if (!query.exec() || !query.next())
        {
            throw QSqlError("Source folder not found", "Source folder does not exist");
        }

        int currentParentId = query.value("parent_id").toInt();

        // 2. 如果目标文件夹与当前父文件夹相同，直接返回成功
        if (currentParentId == targetFolderId)
        {
            qDebug() << "📁 [DatabaseApi] 文件夹已在目标文件夹中，无需移动";
            result["success"] = true;
            result["message"] = "文件夹已在目标文件夹中";
            m_db.commit();
            return result;
        }

        // 3. 如果目标文件夹不是根目录，检查目标文件夹是否存在
        if (targetFolderId != -1)
        {
            query.prepare("SELECT id FROM folders WHERE id = ?");
            query.addBindValue(targetFolderId);
            if (!query.exec() || !query.next())
            {
                throw QSqlError("Target folder not found", "Target folder does not exist");
            }
        }

        // 4. 检查循环引用：不能移动到自己或自己的后代文件夹
        if (folderId == targetFolderId)
        {
            throw QSqlError("Cannot move to self", "Cannot move folder to itself");
        }

        // 只有当目标不是根目录时才需要检查循环引用
        if (targetFolderId != -1)
        {
            // 递归检查是否会造成循环引用
            int checkParentId = targetFolderId;
            while (checkParentId != -1)
            {
                if (checkParentId == folderId)
                {
                    throw QSqlError("Circular reference", "Cannot move folder to its descendant");
                }

                query.prepare("SELECT parent_id FROM folders WHERE id = ?");
                query.addBindValue(checkParentId);
                if (!query.exec() || !query.next())
                {
                    break;
                }
                checkParentId = query.value("parent_id").toInt();
            }
        }

        // 5. 更新文件夹的 parent_id
        // 对于根目录，parent_id 应该设置为 NULL
        if (targetFolderId == -1)
        {
            query.prepare("UPDATE folders SET parent_id = NULL WHERE id = ?");
            query.addBindValue(folderId);
        }
        else
        {
            query.prepare("UPDATE folders SET parent_id = ? WHERE id = ?");
            query.addBindValue(targetFolderId);
            query.addBindValue(folderId);
        }

        if (!query.exec())
        {
            throw query.lastError();
        }

        // 6. 更新 folder_folder_rel 表中的关联关系
        // 6.1 删除旧的关联关系
        query.prepare("DELETE FROM folder_folder_rel WHERE child_id = ?");
        query.addBindValue(folderId);
        if (!query.exec())
        {
            throw query.lastError();
        }

        // 6.2 创建新的关联关系
        int actualParentId = (targetFolderId == -1) ? -1 : targetFolderId; // 根文件夹使用 -1 作为 parent_id

        // 将目标文件夹中现有文件夹的sort_order都+1，为新文件夹腾出第一位
        query.prepare("UPDATE folder_folder_rel SET sort_order = sort_order + 1 WHERE parent_id = ?");
        query.addBindValue(actualParentId);
        if (!query.exec())
        {
            throw query.lastError();
        }

        // 插入新的关联关系，sort_order设为0（排在第一位）
        query.prepare("INSERT INTO folder_folder_rel (parent_id, child_id, sort_order) VALUES (?, ?, 0)");
        query.addBindValue(actualParentId);
        query.addBindValue(folderId);
        if (!query.exec())
        {
            throw query.lastError();
        }

        m_db.commit();
        result["success"] = true;
        result["message"] = QString("文件夹已成功移动到%1").arg(targetFolderId == -1 ? "根目录" : QString("文件夹%1").arg(targetFolderId));
        qDebug() << "✅ [DatabaseApi] 文件夹移动完成";
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "❌ [DatabaseApi] 移动文件夹失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

// 新增：专门用于重命名文档的方法
QJsonObject DatabaseApi::renameDocument(int id, const QString &newTitle)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        qDebug() << "📝 [DatabaseApi] 重命名文档 ID:" << id << "新标题:" << newTitle;

        // 只更新文档标题，不修改内容和其他字段
        query.prepare("UPDATE documents SET title = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        query.addBindValue(newTitle);
        query.addBindValue(id);

        if (!query.exec())
        {
            throw query.lastError();
        }

        // 检查是否有文档被更新
        int rowsAffected = query.numRowsAffected();
        if (rowsAffected == 0)
        {
            result["success"] = false;
            result["error"] = QString("Document with ID %1 not found").arg(id);
            return result;
        }

        qDebug() << "✅ [DatabaseApi] 文档重命名成功 ID:" << id << "新标题:" << newTitle;

        result["success"] = true;
        result["message"] = QString("Document renamed successfully to '%1'").arg(newTitle);
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 重命名文档失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::removeDocumentKnowledgeAssociation(int documentId, int knowledgeBaseId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        qDebug() << "🔗 [DatabaseApi] 开始移除文档知识库关联:" << documentId << "from KB:" << knowledgeBaseId;

        // 删除特定的关联记录
        query.prepare("DELETE FROM document_knowledge_associations WHERE document_id = ? AND knowledge_base_id = ?");
        query.addBindValue(documentId);
        query.addBindValue(knowledgeBaseId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        int removedCount = query.numRowsAffected();

        result["success"] = true;
        result["message"] = QString("Successfully removed %1 document association").arg(removedCount);
        result["removed_count"] = removedCount;

        qDebug() << "✅ [DatabaseApi] 已移除" << removedCount << "个文档关联";
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 移除文档关联失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
        result["message"] = QString("Remove document association failed: %1").arg(error.text());
    }

    return result;
}

QJsonObject DatabaseApi::clearDocumentKnowledgeAssociation(const QString &knowledgeDocumentId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        qDebug() << "🔗 [DatabaseApi] 开始清除知识库文档关联:" << knowledgeDocumentId;

        // 删除指定知识库文档的所有关联记录
        query.prepare("DELETE FROM document_knowledge_associations WHERE knowledge_document_id = ?");
        query.addBindValue(knowledgeDocumentId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        int clearedCount = query.numRowsAffected();

        result["success"] = true;
        result["message"] = QString("Successfully cleared %1 document associations").arg(clearedCount);
        result["cleared_count"] = clearedCount;

        qDebug() << "✅ [DatabaseApi] 已清除" << clearedCount << "个文档关联";
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 清除文档关联失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
        result["message"] = QString("Clear document association failed: %1").arg(error.text());
    }

    return result;
}

QJsonObject DatabaseApi::getOriginalDocumentByKnowledgeDocumentId(int knowledgeDocumentId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        qDebug() << "🔍 [DatabaseApi] 根据知识库文档ID获取原始文档:" << knowledgeDocumentId;

        // 通过关联表查找原始文档
        query.prepare("SELECT d.id, d.title, d.content, d.metadata, d.created_at, d.updated_at "
                      "FROM documents d "
                      "INNER JOIN document_knowledge_associations dka ON d.id = dka.document_id "
                      "WHERE dka.knowledge_document_id = ?");
        query.addBindValue(knowledgeDocumentId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        if (query.next())
        {
            result["success"] = true;
            result["id"] = query.value("id").toInt();
            result["title"] = query.value("title").toString();
            result["content"] = query.value("content").toString();
            result["metadata"] = query.value("metadata").toString();
            result["created_at"] = query.value("created_at").toString();
            result["updated_at"] = query.value("updated_at").toString();

            qDebug() << "✅ [DatabaseApi] 找到关联的原始文档:" << result["title"].toString();
        }
        else
        {
            result["success"] = false;
            result["message"] = "No associated original document found";
            qDebug() << "📋 [DatabaseApi] 未找到关联的原始文档";
        }
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 获取原始文档失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
        result["message"] = QString("Get original document failed: %1").arg(error.text());
    }

    return result;
}

QJsonObject DatabaseApi::clearKnowledgeBaseAssociations(int knowledgeBaseId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        qDebug() << "🔗 [DatabaseApi] 开始清除知识库的所有文档关联:" << knowledgeBaseId;

        // 删除指定知识库的所有关联记录
        query.prepare("DELETE FROM document_knowledge_associations WHERE knowledge_base_id = ?");
        query.addBindValue(knowledgeBaseId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        int clearedCount = query.numRowsAffected();

        result["success"] = true;
        result["message"] = QString("Successfully cleared %1 knowledge base associations").arg(clearedCount);
        result["cleared_count"] = clearedCount;

        qDebug() << "✅ [DatabaseApi] 已清除知识库" << knowledgeBaseId << "的" << clearedCount << "个文档关联";
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 清除知识库文档关联失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
        result["message"] = QString("Clear knowledge base associations failed: %1").arg(error.text());
    }

    return result;
}

QJsonObject DatabaseApi::updateKnowledgeDocumentAssociationTime(int knowledgeDocumentId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        qDebug() << "🔄 [DatabaseApi] 更新知识库文档关联时间:" << knowledgeDocumentId;

        // 更新指定知识库文档的关联时间戳
        query.prepare("UPDATE document_knowledge_associations SET updated_at = CURRENT_TIMESTAMP WHERE knowledge_document_id = ?");
        query.addBindValue(knowledgeDocumentId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        int updatedCount = query.numRowsAffected();

        result["success"] = true;
        result["message"] = QString("Successfully updated %1 association timestamps").arg(updatedCount);
        result["updated_count"] = updatedCount;

        qDebug() << "✅ [DatabaseApi] 已更新知识库文档" << knowledgeDocumentId << "的" << updatedCount << "个关联时间戳";
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 更新知识库文档关联时间失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
        result["message"] = QString("Update knowledge document association time failed: %1").arg(error.text());
    }

    return result;
}

QJsonObject DatabaseApi::addDocumentKnowledgeAssociation(int documentId, int knowledgeDocumentId, int knowledgeBaseId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        qDebug() << "🔗 [DatabaseApi] 添加文档知识库关联:" << documentId << "to KB:" << knowledgeBaseId << "KDoc:" << knowledgeDocumentId;

        // 检查是否已存在关联
        query.prepare("SELECT COUNT(*) FROM document_knowledge_associations WHERE document_id = ? AND knowledge_base_id = ?");
        query.addBindValue(documentId);
        query.addBindValue(knowledgeBaseId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        if (query.next() && query.value(0).toInt() > 0)
        {
            result["success"] = false;
            result["error"] = "Document already associated with this knowledge base";
            result["message"] = "Document already associated with this knowledge base";
            return result;
        }

        // 添加新的关联
        query.prepare("INSERT INTO document_knowledge_associations (document_id, knowledge_document_id, knowledge_base_id) VALUES (?, ?, ?)");
        query.addBindValue(documentId);
        query.addBindValue(knowledgeDocumentId);
        query.addBindValue(knowledgeBaseId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        result["success"] = true;
        result["message"] = "Document association added successfully";
        result["association_id"] = query.lastInsertId().toInt();

        qDebug() << "✅ [DatabaseApi] 文档关联添加成功";
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 添加文档关联失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
        result["message"] = QString("Add document association failed: %1").arg(error.text());
    }

    return result;
}

QJsonObject DatabaseApi::getDocumentKnowledgeAssociations(int documentId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        qDebug() << "🔍 [DatabaseApi] 获取文档知识库关联:" << documentId;

        query.prepare("SELECT id, knowledge_document_id, knowledge_base_id, created_at FROM document_knowledge_associations WHERE document_id = ?");
        query.addBindValue(documentId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        QJsonArray associations;
        while (query.next())
        {
            QJsonObject association;
            association["id"] = query.value(0).toInt();
            association["knowledge_document_id"] = query.value(1).toInt();
            association["knowledge_base_id"] = query.value(2).toInt();
            association["created_at"] = query.value(3).toString();
            associations.append(association);
        }

        result["success"] = true;
        result["associations"] = associations;
        result["count"] = associations.size();

        qDebug() << "✅ [DatabaseApi] 找到" << associations.size() << "个关联";
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 获取文档关联失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::isDocumentInKnowledgeBase(int documentId, int knowledgeBaseId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        query.prepare("SELECT COUNT(*) FROM document_knowledge_associations WHERE document_id = ? AND knowledge_base_id = ?");
        query.addBindValue(documentId);
        query.addBindValue(knowledgeBaseId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        bool exists = false;
        if (query.next())
        {
            exists = query.value(0).toInt() > 0;
        }

        result["success"] = true;
        result["exists"] = exists;
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 检查文档关联失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::deleteDocument(int id)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 1. 删除文档图片引用（新的引用计数系统）
        QSqlQuery imageRefQuery(m_db);
        imageRefQuery.prepare("DELETE FROM image_references WHERE document_id = ?");
        imageRefQuery.addBindValue(id);
        if (!imageRefQuery.exec())
        {
            throw imageRefQuery.lastError();
        }

        // 清理无引用的图片
        QJsonObject cleanupResult = deleteUnreferencedImages();
        if (!cleanupResult["success"].toBool())
        {
            qWarning() << "Failed to cleanup unreferenced images:" << cleanupResult["error"].toString();
        }
        qCInfo(databaseApi) << "Deleted images for document:" << id;

        // 2. 删除文档关系
        query.prepare("DELETE FROM folder_document_rel WHERE document_id = ?");
        query.addBindValue(id);
        if (!query.exec())
        {
            throw query.lastError();
        }
        qCInfo(databaseApi) << "Deleted folder-document relations for document:" << id;

        // 3. 删除文档关联关系
        query.prepare("DELETE FROM document_document_rel WHERE from_doc_id = ? OR to_doc_id = ?");
        query.addBindValue(id);
        query.addBindValue(id);
        if (!query.exec())
        {
            throw query.lastError();
        }
        qCInfo(databaseApi) << "Deleted document-document relations for document:" << id;

        // 4. 删除文档知识库关联
        query.prepare("DELETE FROM document_knowledge_associations WHERE document_id = ?");
        query.addBindValue(id);
        if (!query.exec())
        {
            throw query.lastError();
        }
        qCInfo(databaseApi) << "Deleted document-knowledge associations for document:" << id;

        // 5. 删除文档
        query.prepare("DELETE FROM documents WHERE id = ?");
        query.addBindValue(id);
        if (!query.exec())
        {
            throw query.lastError();
        }
        qCInfo(databaseApi) << "Deleted document:" << id;

        m_db.commit();
        result["success"] = true;
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qCWarning(databaseApi) << "Error: Failed to delete document:" << id << "Error:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::listDocuments(int folder_id)
{
    QJsonObject result;
    QJsonArray documents;
    QSqlQuery query(m_db);

    try
    {
        // 只通过 folder_document_rel 查询
        query.prepare("SELECT d.*, r.sort_order FROM documents d "
                      "JOIN folder_document_rel r ON d.id = r.document_id "
                      "WHERE r.folder_id = ? "
                      "ORDER BY r.sort_order ASC");
        query.addBindValue(folder_id);

        if (!query.exec())
        {
            throw query.lastError();
        }

        while (query.next())
        {
            QJsonObject doc;
            doc["id"] = toJsonIdSafe(query.value("id"));
            doc["title"] = query.value("title").toString();
            doc["content"] = query.value("content").toString();
            doc["created_at"] = query.value("created_at").toString();
            doc["updated_at"] = query.value("updated_at").toString();
            doc["sort_order"] = query.value("sort_order").toInt();

            documents.append(doc);
        }

        result["success"] = true;
        result["documents"] = documents;
    }
    catch (const QSqlError &error)
    {
        qDebug() << "Error: Failed to list documents:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::getAllDocumentsInFolder(int folder_id)
{
    QJsonObject result;
    QJsonArray documentIds;
    QSqlQuery query(m_db);

    try
    {
        // 使用递归CTE查询指定文件夹及其所有子文件夹下的文档ID
        QString recursiveQuery = R"(
            WITH RECURSIVE folder_tree AS (
                -- 起始条件：包含指定文件夹本身
                SELECT id, parent_id, 0 as level
                FROM folders
                WHERE id = ?
                UNION ALL
                -- 递归条件：查找所有子文件夹，不限制层级深度
                SELECT f.id, f.parent_id, ft.level + 1
                FROM folders f
                JOIN folder_folder_rel r ON f.id = r.child_id
                JOIN folder_tree ft ON r.parent_id = ft.id
            )
            SELECT DISTINCT d.id
            FROM folder_tree ft
            JOIN folder_document_rel fdr ON ft.id = fdr.folder_id
            JOIN documents d ON fdr.document_id = d.id
            ORDER BY d.id ASC
        )";

        query.prepare(recursiveQuery);
        query.addBindValue(folder_id);

        if (!query.exec())
        {
            throw query.lastError();
        }

        while (query.next())
        {
            documentIds.append(toJsonIdSafe(query.value("id")));
        }

        result["success"] = true;
        result["document_ids"] = documentIds;
        result["count"] = documentIds.size();
    }
    catch (const QSqlError &error)
    {
        qDebug() << "Error: Failed to get all documents in folder:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::getSetting(const QString &key)
{
    QJsonObject result;
    QSqlQuery query;
    query.prepare("SELECT value FROM settings WHERE key = ?");
    query.addBindValue(key);

    if (query.exec() && query.next())
    {
        result["success"] = true;
        result["value"] = query.value("value").toString();
    }
    else
    {
        result["success"] = false;
        result["error"] = "Setting not found";
    }

    return result;
}

QJsonObject DatabaseApi::setSetting(const QString &key, const QString &value)
{
    QJsonObject result;
    QSqlQuery query;
    query.prepare("INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)");
    query.addBindValue(key);
    query.addBindValue(value);

    if (!query.exec())
    {
        qDebug() << "Error: Failed to set setting:" << query.lastError().text();
        result["success"] = false;
        result["error"] = query.lastError().text();
        return result;
    }

    result["success"] = true;
    return result;
}

// 文件夹相关方法实现
QJsonObject DatabaseApi::createFolder(const QString &name, int parent_id)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 1. 创建文件夹，同时设置 parent_id
        if (parent_id <= 0)
        {
            // 当 parent_id 为 -1 或 null 时，设置为 NULL
            query.prepare("INSERT INTO folders (name, parent_id) VALUES (?, NULL)");
            query.addBindValue(name);
        }
        else
        {
            query.prepare("INSERT INTO folders (name, parent_id) VALUES (?, ?)");
            query.addBindValue(name);
            query.addBindValue(parent_id);
        }

        if (!query.exec())
        {
            throw query.lastError();
        }

        int folderId = query.lastInsertId().toInt();

        // 2. 创建关联关系（所有文件夹都需要在 folder_folder_rel 表中有记录）
        int actualParentId = (parent_id <= 0) ? -1 : parent_id; // 根文件夹使用 -1 作为 parent_id

        // 获取当前最大排序值
        query.prepare("SELECT MAX(sort_order) FROM folder_folder_rel WHERE parent_id = ?");
        query.addBindValue(actualParentId);
        if (!query.exec() || !query.next())
        {
            throw query.lastError();
        }

        int maxSortOrder = query.value(0).isNull() ? -1 : query.value(0).toInt();
        int newSortOrder = maxSortOrder + 1;

        // 创建关联关系（根文件夹的 parent_id 为 -1）
        query.prepare("INSERT INTO folder_folder_rel (parent_id, child_id, sort_order) VALUES (?, ?, ?)");
        query.addBindValue(actualParentId);
        query.addBindValue(folderId);
        query.addBindValue(newSortOrder);

        if (!query.exec())
        {
            throw query.lastError();
        }

        qDebug() << "Created folder with ID:" << folderId << "parent_id:" << actualParentId << "sort_order:" << newSortOrder;

        m_db.commit();
        result["success"] = true;
        result["id"] = folderId;
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "Error: Failed to create folder:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::getFolder(int id)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    // 获取文件夹基本信息
    query.prepare("SELECT * FROM folders WHERE id = ?");
    query.addBindValue(id);

    if (query.exec() && query.next())
    {
        result["success"] = true;
        result["id"] = toJsonIdSafe(query.value("id"));
        result["name"] = query.value("name").toString();
        result["parent_id"] = toJsonNullableIdSafe(query.value("parent_id")); // parent_id使用-1表示根文件夹
        result["created_at"] = query.value("created_at").toString();
        result["updated_at"] = query.value("updated_at").toString();
        result["sort_order"] = query.value("sort_order").toInt();

        // 查询子文件夹及排序，并统计每个子文件夹的子文件夹数量
        QJsonArray children;
        QSqlQuery subQuery(m_db);
        subQuery.prepare("SELECT f.*, r.sort_order, "
                         "(SELECT COUNT(*) FROM folders sf WHERE sf.parent_id = f.id) AS subfolder_count "
                         "FROM folders f "
                         "JOIN folder_folder_rel r ON f.id = r.child_id "
                         "WHERE r.parent_id = ? "
                         "ORDER BY r.sort_order ASC");
        subQuery.addBindValue(id);
        if (subQuery.exec())
        {
            while (subQuery.next())
            {
                QJsonObject child;
                child["id"] = toJsonIdSafe(subQuery.value("id"));
                child["name"] = subQuery.value("name").toString();
                child["parent_id"] = toJsonNullableIdSafe(subQuery.value("parent_id")); // parent_id使用-1表示根文件夹
                child["created_at"] = subQuery.value("created_at").toString();
                child["updated_at"] = subQuery.value("updated_at").toString();
                child["sort_order"] = subQuery.value("sort_order").toInt();
                child["children"] = QJsonArray();  // 初始化为空数组，与 listFolders 保持一致
                child["documents"] = QJsonArray(); // 初始化为空数组，与 listFolders 保持一致
                children.append(child);
            }
        }
        result["children"] = children;

        // 查询文件夹内文档及排序
        QJsonArray documents;
        QSqlQuery docQuery(m_db);
        docQuery.prepare("SELECT d.*, r.sort_order FROM documents d "
                         "JOIN folder_document_rel r ON d.id = r.document_id "
                         "WHERE r.folder_id = ? "
                         "ORDER BY r.sort_order ASC");
        docQuery.addBindValue(id);
        if (docQuery.exec())
        {
            while (docQuery.next())
            {
                QJsonObject doc;
                doc["id"] = toJsonIdSafe(docQuery.value("id"));
                doc["title"] = docQuery.value("title").toString();
                doc["content"] = docQuery.value("content").toString();
                doc["created_at"] = docQuery.value("created_at").toString();
                doc["updated_at"] = docQuery.value("updated_at").toString();
                doc["sort_order"] = docQuery.value("sort_order").toInt();

                documents.append(doc);
            }
        }
        result["documents"] = documents;
    }
    else
    {
        result["success"] = false;
        result["error"] = "folder not found";
    }

    return result;
}

QJsonObject DatabaseApi::updateFolder(int id, const QString &name, int parent_id)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 1. 更新文件夹基本信息（包括 parent_id 字段）
        // 对于根文件夹，parent_id 应该设置为 NULL 以满足外键约束
        if (parent_id <= 0)
        {
            query.prepare("UPDATE folders SET name = ?, parent_id = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            query.addBindValue(name);
            query.addBindValue(id);
        }
        else
        {
            query.prepare("UPDATE folders SET name = ?, parent_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            query.addBindValue(name);
            query.addBindValue(parent_id);
            query.addBindValue(id);
        }

        if (!query.exec())
        {
            throw query.lastError();
        }

        // 2. 处理父文件夹关系
        // 2.1 获取当前父文件夹ID
        query.prepare("SELECT parent_id FROM folder_folder_rel WHERE child_id = ?");
        query.addBindValue(id);
        if (!query.exec())
        {
            throw query.lastError();
        }

        int currentParentId = -1; // 默认值设为 -1，表示根文件夹
        if (query.next())
        {
            currentParentId = query.value("parent_id").toInt();
        }

        // 2.2 如果父文件夹发生变化
        if (currentParentId != parent_id)
        {
            // 删除旧关系（无论 parent_id 是什么值都删除，因为 child_id 是主键）
            query.prepare("DELETE FROM folder_folder_rel WHERE child_id = ?");
            query.addBindValue(id);
            if (!query.exec())
            {
                throw query.lastError();
            }

            // 创建新关系（包括根文件夹，保持与创建文件夹时的逻辑一致）
            int actualParentId = (parent_id <= 0) ? -1 : parent_id; // 根文件夹使用 -1 作为 parent_id

            // 获取新父文件夹下的最大排序值
            query.prepare("SELECT MAX(sort_order) FROM folder_folder_rel WHERE parent_id = ?");
            query.addBindValue(actualParentId);
            if (!query.exec() || !query.next())
            {
                throw query.lastError();
            }

            int maxSortOrder = query.value(0).isNull() ? -1 : query.value(0).toInt();
            int newSortOrder = maxSortOrder + 1;

            // 创建关系记录（根文件夹的 parent_id 为 -1）
            query.prepare("INSERT INTO folder_folder_rel (parent_id, child_id, sort_order) VALUES (?, ?, ?)");
            query.addBindValue(actualParentId);
            query.addBindValue(id);
            query.addBindValue(newSortOrder);

            if (!query.exec())
            {
                throw query.lastError();
            }
        }

        m_db.commit();
        result["success"] = true;
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "Error: Failed to update folder:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::deleteFolder(int id)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 递归删除文件夹及其所有内容
        QJsonObject deleteResult = recursiveDeleteFolder(id);
        if (!deleteResult["success"].toBool())
        {
            throw QSqlError("Failed to delete folder", deleteResult["error"].toString());
        }

        m_db.commit();
        result["success"] = true;
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "Error: Failed to delete folder:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::recursiveDeleteFolder(int folderId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        // 1. 递归删除所有子文件夹
        query.prepare("SELECT child_id FROM folder_folder_rel WHERE parent_id = ?");
        query.addBindValue(folderId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        QList<int> childFolders;
        while (query.next())
        {
            childFolders.append(query.value(0).toInt());
        }

        // 递归删除子文件夹
        for (int childId : childFolders)
        {
            QJsonObject childResult = recursiveDeleteFolder(childId);
            if (!childResult["success"].toBool())
            {
                throw QSqlError("Failed to delete child folder", childResult["error"].toString());
            }
        }

        // 2. 删除该文件夹内的所有文档
        query.prepare("SELECT document_id FROM folder_document_rel WHERE folder_id = ?");
        query.addBindValue(folderId);

        if (!query.exec())
        {
            throw query.lastError();
        }

        QList<int> documents;
        while (query.next())
        {
            documents.append(query.value(0).toInt());
        }

        // 删除文档及其关联信息
        for (int docId : documents)
        {
            QJsonObject docResult = deleteDocument(docId);
            if (!docResult["success"].toBool())
            {
                throw QSqlError("Failed to delete document", docResult["error"].toString());
            }
        }

        // 3. 删除文件夹关系
        query.prepare("DELETE FROM folder_folder_rel WHERE child_id = ?");
        query.addBindValue(folderId);
        if (!query.exec())
        {
            throw query.lastError();
        }

        // 4. 删除文件夹
        query.prepare("DELETE FROM folders WHERE id = ?");
        query.addBindValue(folderId);
        if (!query.exec())
        {
            throw query.lastError();
        }

        result["success"] = true;
    }
    catch (const QSqlError &error)
    {
        qDebug() << "Error: Failed to recursively delete folder:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::listFolders(int parent_id)
{
    QJsonObject result;
    QJsonArray folders;
    QSqlQuery query(m_db);

    try
    {
        QString recursiveQuery;
        if (parent_id <= 0)
        {
            // 查询根文件夹及其子孙（根文件夹在 folder_folder_rel 表中的 parent_id 为 -1）
            recursiveQuery = R"(
                WITH RECURSIVE folder_tree AS (
                    SELECT f.id, f.name, f.parent_id, f.created_at, f.updated_at,
                           r.sort_order, 1 as level, f.id as root_id
                    FROM folders f
                    JOIN folder_folder_rel r ON f.id = r.child_id
                    WHERE r.parent_id = -1
                    UNION ALL
                    SELECT f.id, f.name, f.parent_id, f.created_at, f.updated_at,
                           r.sort_order, ft.level + 1, ft.root_id
                    FROM folders f
                    JOIN folder_folder_rel r ON f.id = r.child_id
                    JOIN folder_tree ft ON r.parent_id = ft.id
                    WHERE ft.level < 2
                ),
                folder_docs AS (
                    SELECT f.id as folder_id, d.id as doc_id, d.title, d.content,
                           d.created_at, d.updated_at, r.sort_order,
                           dka.created_at as add_knowledge_at,
                           dka.knowledge_document_id
                    FROM folder_tree f
                    JOIN folder_document_rel r ON f.id = r.folder_id
                    JOIN documents d ON r.document_id = d.id
                    LEFT JOIN document_knowledge_associations dka ON d.id = dka.document_id
                    WHERE f.level <= 2
                )
                SELECT ft.*, 
                       (SELECT json_group_array(json_object(
                         'id', fd.doc_id,
                         'title', fd.title,
                         'content', fd.content,
                         'created_at', fd.created_at,
                         'updated_at', fd.updated_at,
                         'add_knowledge_at', fd.add_knowledge_at,
                         'knowledge_document_id', fd.knowledge_document_id,
                         'sort_order', fd.sort_order))
                        FROM folder_docs fd
                        WHERE fd.folder_id = ft.id) as documents_json,
                       (SELECT json_group_array(json_object(
                         'id', child.id,
                         'name', child.name,
                         'parent_id', child.parent_id,
                         'created_at', child.created_at,
                         'updated_at', child.updated_at,
                         'sort_order', child.sort_order,
                         'documents', (SELECT json_group_array(json_object(
                           'id', cd.doc_id))
                           FROM folder_docs cd
                           WHERE cd.folder_id = child.id)))
                        FROM folder_tree child
                        WHERE child.parent_id = ft.id AND child.level = 2) as children_json
                FROM folder_tree ft 
                WHERE ft.level = 1 
                ORDER BY ft.sort_order ASC, ft.created_at ASC
            )";
            query.prepare(recursiveQuery);
        }
        else
        {
            // 查询指定父文件夹及其子孙
            recursiveQuery = R"(
                WITH RECURSIVE folder_tree AS (
                    SELECT f.id, f.name, f.parent_id, f.created_at, f.updated_at,
                           r.sort_order, 1 as level, f.id as root_id
                    FROM folders f
                    JOIN folder_folder_rel r ON f.id = r.child_id
                    WHERE r.parent_id = ?
                    UNION ALL
                    SELECT f.id, f.name, f.parent_id, f.created_at, f.updated_at,
                           r.sort_order, ft.level + 1, ft.root_id
                    FROM folders f
                    JOIN folder_folder_rel r ON f.id = r.child_id
                    JOIN folder_tree ft ON r.parent_id = ft.id
                    WHERE ft.level < 2
                ),
                folder_docs AS (
                    SELECT f.id as folder_id, d.id as doc_id, d.title, d.content,
                           d.created_at, d.updated_at, r.sort_order,
                           dka.created_at as add_knowledge_at,
                           dka.knowledge_document_id
                    FROM folder_tree f
                    JOIN folder_document_rel r ON f.id = r.folder_id
                    JOIN documents d ON r.document_id = d.id
                    LEFT JOIN document_knowledge_associations dka ON d.id = dka.document_id
                    WHERE f.level <= 2
                )
                SELECT ft.*, 
                       (SELECT json_group_array(json_object(
                         'id', fd.doc_id,
                         'title', fd.title,
                         'content', fd.content,
                         'created_at', fd.created_at,
                         'updated_at', fd.updated_at,
                         'add_knowledge_at', fd.add_knowledge_at,
                         'knowledge_document_id', fd.knowledge_document_id,
                         'sort_order', fd.sort_order))
                        FROM folder_docs fd
                        WHERE fd.folder_id = ft.id) as documents_json,
                       (SELECT json_group_array(json_object(
                         'id', child.id,
                         'name', child.name,
                         'parent_id', child.parent_id,
                         'created_at', child.created_at,
                         'updated_at', child.updated_at,
                         'sort_order', child.sort_order,
                         'documents', (SELECT json_group_array(json_object(
                           'id', cd.doc_id))
                           FROM folder_docs cd
                           WHERE cd.folder_id = child.id)))
                        FROM folder_tree child
                        WHERE child.parent_id = ft.id AND child.level = 2) as children_json
                FROM folder_tree ft 
                WHERE ft.level = 1 
                ORDER BY ft.sort_order ASC, ft.created_at ASC
            )";
            query.prepare(recursiveQuery);
            query.addBindValue(parent_id);
        }
        if (!query.exec())
        {
            throw query.lastError();
        }
        while (query.next())
        {
            QJsonObject folder;
            folder["id"] = toJsonIdSafe(query.value("id"));
            folder["name"] = query.value("name").toString();
            folder["parent_id"] = toJsonNullableIdSafe(query.value("parent_id")); // parent_id使用-1表示根文件夹
            folder["created_at"] = query.value("created_at").toString();
            folder["updated_at"] = query.value("updated_at").toString();
            folder["sort_order"] = toJsonIntOrNull(query.value("sort_order"));
            // 解析文档JSON
            QString documentsJson = query.value("documents_json").toString();
            if (!documentsJson.isEmpty() && documentsJson != "null")
            {
                QJsonDocument doc = QJsonDocument::fromJson(documentsJson.toUtf8());
                folder["documents"] = doc.array();
            }
            else
            {
                folder["documents"] = QJsonArray();
            }
            // 解析子文件夹JSON
            QString childrenJson = query.value("children_json").toString();
            if (!childrenJson.isEmpty() && childrenJson != "null")
            {
                QJsonDocument doc = QJsonDocument::fromJson(childrenJson.toUtf8());
                folder["children"] = doc.array();
            }
            else
            {
                folder["children"] = QJsonArray();
            }
            folders.append(folder);
        }
        result["success"] = true;
        result["folders"] = folders;
    }
    catch (const QSqlError &error)
    {
        qDebug() << "Error: Failed to list folders:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }
    return result;
}

// 新增：更新排序方法
QJsonObject DatabaseApi::updateFolderOrder(int parent_id, const QList<int> &folder_ids)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 批量更新文件夹排序
        qDebug() << "Updating folder order for parent_id:" << parent_id << "with" << folder_ids.size() << "folders";
        for (int i = 0; i < folder_ids.size(); ++i)
        {
            query.prepare("UPDATE folder_folder_rel SET sort_order = ? WHERE parent_id = ? AND child_id = ?");
            query.addBindValue(i);
            query.addBindValue(parent_id);
            query.addBindValue(folder_ids[i]);
            qDebug() << "Setting folder" << folder_ids[i] << "sort_order to" << i;
            if (!query.exec())
            {
                throw query.lastError();
            }
        }

        m_db.commit();
        result["success"] = true;
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "Error: Failed to update folder order:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::updateDocumentOrder(int folder_id, const QList<int> &document_ids)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 批量更新文档排序
        qDebug() << "Updating document order for folder_id:" << folder_id << "with" << document_ids.size() << "documents";
        for (int i = 0; i < document_ids.size(); ++i)
        {
            query.prepare("UPDATE folder_document_rel SET sort_order = ? WHERE folder_id = ? AND document_id = ?");
            query.addBindValue(i);
            query.addBindValue(folder_id);
            query.addBindValue(document_ids[i]);
            qDebug() << "Setting document" << document_ids[i] << "sort_order to" << i;
            if (!query.exec())
            {
                throw query.lastError();
            }
        }

        m_db.commit();
        result["success"] = true;
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "Error: Failed to update document order:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

// 工具函数：将 QJsonObject 转为 JS 对象并回调
void DatabaseApi::callWithJson(QJSValue callback, const QJsonObject &result)
{
    qWarning() << "[DatabaseApi] callWithJson called";
    qWarning() << "[DatabaseApi] result content:" << result;

    if (!callback.isCallable())
    {
        qWarning() << "Callback is not callable!";
        return;
    }

    QJSEngine *engine = qjsEngine(this);
    if (!engine)
    {
        qWarning() << "No QJSEngine found!";
        return;
    }

    QJsonDocument doc(result);
    QString jsonString = QString::fromUtf8(doc.toJson(QJsonDocument::Compact));
    qWarning() << "[DatabaseApi] JSON string:" << jsonString;

    QJSValue jsResult = engine->evaluate("(" + jsonString + ")");
    qWarning() << "[DatabaseApi] JS result is valid:" << !jsResult.isUndefined();

    callback.call(QList<QJSValue>() << jsResult);
    qWarning() << "[DatabaseApi] callback function called";
}

// 工具函数：安全地将QVariant转换为int，null值转换为默认值
int DatabaseApi::variantToIntSafe(const QVariant &variant, int defaultValue)
{
    if (variant.isNull() || !variant.isValid())
    {
        return defaultValue;
    }

    bool ok;
    int value = variant.toInt(&ok);
    return ok ? value : defaultValue;
}

// 异步回调风格实现
void DatabaseApi::createDocument(const QString &title, const QString &content, const QVariant &folder_id, const QString &metadata, QJSValue callback)
{
    int folderId = variantToIntSafe(folder_id, -1); // 使用 -1 作为默认值，表示根文件夹
    QJsonObject result = this->createDocument(title, content, folderId, metadata);
    callWithJson(callback, result);
}

void DatabaseApi::getDocument(const QVariant &id, QJSValue callback)
{
    int docId = variantToIntSafe(id);
    QJsonObject result = this->getDocument(docId);
    callWithJson(callback, result);
}

void DatabaseApi::updateDocument(const QVariant &id, const QString &title, const QString &content, const QVariant &folder_id, const QString &metadata, QJSValue callback)
{
    int docId = variantToIntSafe(id);
    int folderId = variantToIntSafe(folder_id, -1); // 使用 -1 作为默认值，表示根文件夹
    QJsonObject result = this->updateDocument(docId, title, content, folderId, metadata);
    callWithJson(callback, result);
}

void DatabaseApi::moveDocument(const QVariant &documentId, const QVariant &targetFolderId, QJSValue callback)
{
    int docId = variantToIntSafe(documentId);
    int folderId = variantToIntSafe(targetFolderId, -1); // 使用 -1 作为默认值，表示根文件夹
    QJsonObject result = this->moveDocument(docId, folderId);
    callWithJson(callback, result);
}

void DatabaseApi::moveFolder(const QVariant &folderId, const QVariant &targetFolderId, QJSValue callback)
{
    int folderIdInt = variantToIntSafe(folderId);
    int targetFolderIdInt = variantToIntSafe(targetFolderId, -1); // 使用 -1 作为默认值，表示根文件夹
    QJsonObject result = this->moveFolder(folderIdInt, targetFolderIdInt);
    callWithJson(callback, result);
}

void DatabaseApi::addDocumentKnowledgeAssociation(const QVariant &documentId, const QVariant &knowledgeDocumentId, const QVariant &knowledgeBaseId, QJSValue callback)
{
    int docId = variantToIntSafe(documentId);
    int kDocId = variantToIntSafe(knowledgeDocumentId);
    int kbId = variantToIntSafe(knowledgeBaseId);
    QJsonObject result = this->addDocumentKnowledgeAssociation(docId, kDocId, kbId);
    callWithJson(callback, result);
}

void DatabaseApi::removeDocumentKnowledgeAssociation(const QVariant &documentId, const QVariant &knowledgeBaseId, QJSValue callback)
{
    int docId = variantToIntSafe(documentId);
    int kbId = variantToIntSafe(knowledgeBaseId);
    QJsonObject result = this->removeDocumentKnowledgeAssociation(docId, kbId);
    callWithJson(callback, result);
}

void DatabaseApi::clearDocumentKnowledgeAssociation(const QString &knowledgeDocumentId, QJSValue callback)
{
    QJsonObject result = this->clearDocumentKnowledgeAssociation(knowledgeDocumentId);
    callWithJson(callback, result);
}

void DatabaseApi::getDocumentKnowledgeAssociations(const QVariant &documentId, QJSValue callback)
{
    int docId = variantToIntSafe(documentId);
    QJsonObject result = this->getDocumentKnowledgeAssociations(docId);
    callWithJson(callback, result);
}

void DatabaseApi::getOriginalDocumentByKnowledgeDocumentId(const QVariant &knowledgeDocumentId, QJSValue callback)
{
    qDebug() << "🔍 [DatabaseApi] getOriginalDocumentByKnowledgeDocumentId was called";
    qDebug() << "📋 [DatabaseApi] parameter type:" << knowledgeDocumentId.typeName();
    qDebug() << "📋 [DatabaseApi] parameter value:" << knowledgeDocumentId;
    qDebug() << "📋 [DatabaseApi] callback is callable:" << callback.isCallable();

    int kDocId = variantToIntSafe(knowledgeDocumentId);
    qDebug() << "📋 [DatabaseApi] converted ID:" << kDocId;

    QJsonObject result = this->getOriginalDocumentByKnowledgeDocumentId(kDocId);
    callWithJson(callback, result);
}

void DatabaseApi::clearKnowledgeBaseAssociations(const QVariant &knowledgeBaseId, QJSValue callback)
{
    qDebug() << "🔍 [DatabaseApi] clearKnowledgeBaseAssociations was called";
    qDebug() << "📋 [DatabaseApi] parameter type:" << knowledgeBaseId.typeName();
    qDebug() << "📋 [DatabaseApi] parameter value:" << knowledgeBaseId;
    qDebug() << "📋 [DatabaseApi] callback is callable:" << callback.isCallable();

    int kbId = variantToIntSafe(knowledgeBaseId);
    qDebug() << "📋 [DatabaseApi] converted ID:" << kbId;

    QJsonObject result = this->clearKnowledgeBaseAssociations(kbId);
    callWithJson(callback, result);
}

void DatabaseApi::updateKnowledgeDocumentAssociationTime(const QVariant &knowledgeDocumentId, QJSValue callback)
{
    qDebug() << "🔍 [DatabaseApi] updateKnowledgeDocumentAssociationTime was called";
    qDebug() << "📋 [DatabaseApi] parameter type:" << knowledgeDocumentId.typeName();
    qDebug() << "📋 [DatabaseApi] parameter value:" << knowledgeDocumentId;
    qDebug() << "📋 [DatabaseApi] callback is callable:" << callback.isCallable();

    int kDocId = variantToIntSafe(knowledgeDocumentId);
    qDebug() << "📋 [DatabaseApi] converted ID:" << kDocId;

    QJsonObject result = this->updateKnowledgeDocumentAssociationTime(kDocId);
    callWithJson(callback, result);
}

void DatabaseApi::isDocumentInKnowledgeBase(const QVariant &documentId, const QVariant &knowledgeBaseId, QJSValue callback)
{
    int docId = variantToIntSafe(documentId);
    int kbId = variantToIntSafe(knowledgeBaseId);
    QJsonObject result = this->isDocumentInKnowledgeBase(docId, kbId);
    callWithJson(callback, result);
}

void DatabaseApi::deleteDocument(const QVariant &id, QJSValue callback)
{
    int docId = variantToIntSafe(id);
    QJsonObject result = this->deleteDocument(docId);
    callWithJson(callback, result);
}

void DatabaseApi::listDocuments(const QVariant &folder_id, QJSValue callback)
{
    int folderId = variantToIntSafe(folder_id, -1); // 使用 -1 作为默认值，表示根文件夹
    QJsonObject result = this->listDocuments(folderId);
    callWithJson(callback, result);
}

void DatabaseApi::getAllDocumentsInFolder(const QVariant &folder_id, QJSValue callback)
{
    int folderId = variantToIntSafe(folder_id, -1); // 使用 -1 作为默认值，表示根文件夹
    QJsonObject result = this->getAllDocumentsInFolder(folderId);
    callWithJson(callback, result);
}

void DatabaseApi::createFolder(const QString &name, const QVariant &parent_id, QJSValue callback)
{
    int parentId = variantToIntSafe(parent_id, -1); // 使用 -1 作为默认值，表示根文件夹
    QJsonObject result = this->createFolder(name, parentId);
    callWithJson(callback, result);
}

void DatabaseApi::getFolder(const QVariant &id, QJSValue callback)
{
    int folderId = variantToIntSafe(id);
    QJsonObject result = this->getFolder(folderId);
    callWithJson(callback, result);
}

void DatabaseApi::updateFolder(const QVariant &id, const QString &name, const QVariant &parent_id, QJSValue callback)
{
    int folderId = variantToIntSafe(id);
    int parentId = variantToIntSafe(parent_id, -1); // 使用 -1 作为默认值，表示根文件夹
    QJsonObject result = this->updateFolder(folderId, name, parentId);
    callWithJson(callback, result);
}

void DatabaseApi::deleteFolder(const QVariant &id, QJSValue callback)
{
    int folderId = variantToIntSafe(id);
    QJsonObject result = this->deleteFolder(folderId);
    callWithJson(callback, result);
}

void DatabaseApi::listFolders(const QVariant &parent_id, QJSValue callback)
{
    int parentId = variantToIntSafe(parent_id, -1); // 使用 -1 作为默认值，表示根文件夹
    QJsonObject result = this->listFolders(parentId);
    callWithJson(callback, result);
}

void DatabaseApi::getSetting(const QString &key, QJSValue callback)
{
    QJsonObject result = this->getSetting(key);
    callWithJson(callback, result);
}

void DatabaseApi::setSetting(const QString &key, const QString &value, QJSValue callback)
{
    QJsonObject result = this->setSetting(key, value);
    callWithJson(callback, result);
}

// 辅助函数：将 QVariant 转换为 QList<int>
QList<int> DatabaseApi::variantToIntList(const QVariant &variant)
{
    QList<int> result;

    qDebug() << "variantToIntList: Input variant type:" << variant.typeName() << "value:" << variant;

    if (variant.canConvert<QVariantList>())
    {
        qDebug() << "Converting as QVariantList";
        QVariantList list = variant.toList();
        qDebug() << "QVariantList size:" << list.size();
        for (int i = 0; i < list.size(); ++i)
        {
            const QVariant &item = list[i];
            bool ok;
            int value = item.toInt(&ok);
            qDebug() << "Item" << i << ":" << item << "-> int:" << value << "ok:" << ok;
            if (ok)
            {
                result.append(value);
            }
            else
            {
                qDebug() << "Warning: Could not convert item to int:" << item;
            }
        }
    }
    else if (variant.canConvert<QJsonArray>())
    {
        qDebug() << "Converting as QJsonArray";
        QJsonArray array = variant.toJsonArray();
        qDebug() << "QJsonArray size:" << array.size();
        for (int i = 0; i < array.size(); ++i)
        {
            const QJsonValue &item = array[i];
            qDebug() << "JSON Item" << i << ":" << item;
            if (item.isDouble())
            {
                int value = static_cast<int>(item.toDouble());
                qDebug() << "Converted to int:" << value;
                result.append(value);
            }
            else
            {
                qDebug() << "Warning: Could not convert JSON item to int:" << item;
            }
        }
    }
    else
    {
        qDebug() << "Warning: Could not convert variant to int list. Type:" << variant.typeName() << "Value:" << variant;
    }

    qDebug() << "variantToIntList: Final result:" << result;
    return result;
}

// 辅助函数：将 QJSValue 数组转换为 QList<int>
QList<int> DatabaseApi::jsValueToIntList(const QJSValue &jsValue)
{
    QList<int> result;

    qDebug() << "jsValueToIntList: Input jsValue isArray:" << jsValue.isArray();

    if (jsValue.isArray())
    {
        int length = jsValue.property("length").toInt();
        qDebug() << "jsValueToIntList: Array length:" << length;

        for (int i = 0; i < length; ++i)
        {
            QJSValue item = jsValue.property(i);
            if (item.isNumber())
            {
                int value = item.toInt();
                qDebug() << "jsValueToIntList: Item" << i << ":" << value;
                result.append(value);
            }
            else
            {
                qDebug() << "Warning: Could not convert JS array item to int:" << item.toString();
            }
        }
    }
    else
    {
        qDebug() << "Warning: QJSValue is not an array:" << jsValue.toString();
    }

    qDebug() << "jsValueToIntList: Final result:" << result;
    return result;
}

// 同步排序方法实现
QString DatabaseApi::reorderFolders(int parent_id, const QVariantList &folder_ids)
{
    qDebug() << "🎯 [DatabaseApi] reorderFolders called with parent_id:" << parent_id << "folder_ids:" << folder_ids;

    try
    {
        QList<int> folderIdList;
        for (const QVariant &variant : folder_ids)
        {
            folderIdList.append(variant.toInt());
        }

        qDebug() << "🎯 [DatabaseApi] After conversion - parentId:" << parent_id << "folderIdList:" << folderIdList;

        QJsonObject result = this->updateFolderOrder(parent_id, folderIdList);
        return QJsonDocument(result).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "🎯 [DatabaseApi] Exception in reorderFolders:" << e.what();
        return QJsonDocument(QJsonObject{{"success", false}, {"error", QString::fromStdString(e.what())}}).toJson(QJsonDocument::Compact);
    }
}

QString DatabaseApi::reorderDocuments(int folder_id, const QVariantList &document_ids)
{
    try
    {
        QList<int> documentIdList;
        for (const QVariant &variant : document_ids)
        {
            documentIdList.append(variant.toInt());
        }

        QJsonObject result = this->updateDocumentOrder(folder_id, documentIdList);
        return QJsonDocument(result).toJson(QJsonDocument::Compact);
    }
    catch (const std::exception &e)
    {
        qDebug() << "🎯 [DatabaseApi] Exception in reorderDocuments:" << e.what();
        return QJsonDocument(QJsonObject{{"success", false}, {"error", QString::fromStdString(e.what())}}).toJson(QJsonDocument::Compact);
    }
}

// 同步版本的搜索方法
// 注意：这里需要调用内部的QJsonObject版本，避免递归调用
QString DatabaseApi::searchFolders(const QString &keyword)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        qDebug() << "🔍 [DatabaseApi] 搜索文件夹，关键字:" << keyword;

        if (keyword.trimmed().isEmpty())
        {
            result["success"] = false;
            result["message"] = "搜索关键字不能为空";
            return QJsonDocument(result).toJson(QJsonDocument::Compact);
        }

        // 解析多个关键词，支持空格、逗号、分号分隔，并自动拆分中英文混合关键词
        QStringList keywords = splitChineseEnglishKeywords(keyword);
        if (keywords.isEmpty())
        {
            result["success"] = false;
            result["message"] = "搜索关键字不能为空";
            return QJsonDocument(result).toJson(QJsonDocument::Compact);
        }

        // 构建动态SQL查询条件
        QStringList whereConditions;
        QStringList bindValues;

        for (const QString &kw : keywords)
        {
            QString trimmedKeyword = kw.trimmed();
            if (!trimmedKeyword.isEmpty())
            {
                whereConditions.append("f.name LIKE ?");
                bindValues.append("%" + trimmedKeyword + "%");
            }
        }

        if (whereConditions.isEmpty())
        {
            result["success"] = false;
            result["message"] = "搜索关键字不能为空";
            return QJsonDocument(result).toJson(QJsonDocument::Compact);
        }

        // 构建完整的SQL查询
        QString sqlQuery = QString(R"(
            SELECT f.*,
                   pf.name as parent_name,
                   (SELECT COUNT(*) FROM folder_document_rel fdr WHERE fdr.folder_id = f.id) as document_count
            FROM folders f
            LEFT JOIN folders pf ON f.parent_id = pf.id
            WHERE %1
            ORDER BY f.name ASC
        )")
                               .arg(whereConditions.join(" AND "));

        qDebug() << "🔍 [DatabaseApi] 执行SQL查询:" << sqlQuery;
        qDebug() << "🔍 [DatabaseApi] 绑定参数:" << bindValues;

        query.prepare(sqlQuery);
        for (const QString &value : bindValues)
        {
            query.addBindValue(value);
        }

        if (!query.exec())
        {
            throw query.lastError();
        }

        QJsonArray results;
        while (query.next())
        {
            QJsonObject folder;
            folder["id"] = toJsonIdSafe(query.value("id"));
            folder["name"] = query.value("name").toString();
            folder["parent_id"] = toJsonNullableIdSafe(query.value("parent_id"));
            folder["parent_name"] = query.value("parent_name").toString();
            folder["document_count"] = query.value("document_count").toInt();
            results.append(folder);
        }

        result["success"] = true;
        result["data"] = results;
        result["message"] = QString("搜索完成，找到 %1 个匹配的文件夹").arg(results.size());
        result["count"] = results.size();

        qDebug() << "✅ [DatabaseApi] 搜索完成，找到" << results.size() << "个文件夹";
        return QJsonDocument(result).toJson(QJsonDocument::Compact);
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 搜索文件夹失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
        result["message"] = QString("搜索文件夹失败: %1").arg(error.text());
        return QJsonDocument(result).toJson(QJsonDocument::Compact);
    }
}

QString DatabaseApi::searchDocuments(const QString &searchText, bool searchInContent, int folderId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        qDebug() << "🔍 [DatabaseApi] 搜索文档，关键字:" << searchText << "搜索内容:" << searchInContent << "文件夹ID:" << folderId;

        if (searchText.trimmed().isEmpty())
        {
            result["success"] = false;
            result["message"] = "搜索关键字不能为空";
            return QJsonDocument(result).toJson(QJsonDocument::Compact);
        }

        QString searchPattern = "%" + searchText + "%";
        QJsonArray results;

        // 构建查询条件
        QString queryStr = R"(
            SELECT d.*, f.name as folder_name, f.id as folder_id,
                   (SELECT COUNT(*) FROM folder_document_rel fdr WHERE fdr.document_id = d.id) as folder_count
            FROM documents d
            LEFT JOIN folder_document_rel fdr ON d.id = fdr.document_id
            LEFT JOIN folders f ON fdr.folder_id = f.id
            WHERE (d.title LIKE ?)
        )";

        // 如果需要搜索内容
        if (searchInContent)
        {
            queryStr += " OR (d.content LIKE ?)";
        }

        // 如果指定了文件夹
        if (folderId > 0)
        {
            queryStr += " AND fdr.folder_id = ?";
        }

        queryStr += " ORDER BY d.title ASC";

        query.prepare(queryStr);
        query.addBindValue(searchPattern);

        if (searchInContent)
        {
            query.addBindValue(searchPattern);
        }

        if (folderId > 0)
        {
            query.addBindValue(folderId);
        }

        if (!query.exec())
        {
            throw query.lastError();
        }

        while (query.next())
        {
            QJsonObject doc;
            doc["id"] = toJsonIdSafe(query.value("id"));
            doc["title"] = query.value("title").toString();
            doc["folder_id"] = toJsonNullableIdSafe(query.value("folder_id"));
            doc["folder_name"] = query.value("folder_name").toString();
            doc["created_at"] = query.value("created_at").toString();
            doc["updated_at"] = query.value("updated_at").toString();

            // 确定匹配类型
            QString title = query.value("title").toString();
            QString content = query.value("content").toString();

            if (title.contains(searchText, Qt::CaseInsensitive))
            {
                doc["match_type"] = "title";
            }
            else if (searchInContent && content.contains(searchText, Qt::CaseInsensitive))
            {
                doc["match_type"] = "content";

                // 提取匹配内容的上下文
                int index = content.indexOf(searchText, 0, Qt::CaseInsensitive);
                if (index >= 0)
                {
                    int start = qMax(0, index - 50);
                    int end = qMin(content.length(), index + searchText.length() + 50);
                    QString preview = content.mid(start, end - start);
                    doc["preview"] = QString("...%1...").arg(preview);
                }
            }
            else
            {
                doc["match_type"] = "title"; // 默认为标题匹配
            }

            results.append(doc);
        }

        result["success"] = true;
        result["data"] = results;
        result["message"] = QString("搜索完成，找到 %1 个匹配的文档").arg(results.size());
        result["count"] = results.size();

        qDebug() << "✅ [DatabaseApi] 文档搜索完成，找到" << results.size() << "个文档";
        return QJsonDocument(result).toJson(QJsonDocument::Compact);
    }
    catch (const QSqlError &error)
    {
        qDebug() << "❌ [DatabaseApi] 搜索文档失败:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
        result["message"] = QString("搜索文档失败: %1").arg(error.text());
        return QJsonDocument(result).toJson(QJsonDocument::Compact);
    }
}

QJsonObject DatabaseApi::getAppSettings()
{
    QJsonObject result;
    QSqlQuery query(m_db);

    query.prepare("SELECT value FROM settings WHERE key = 'app_settings'");

    if (query.exec() && query.next())
    {
        QString settingsJson = query.value("value").toString();
        QJsonDocument doc = QJsonDocument::fromJson(settingsJson.toUtf8());
        if (!doc.isNull() && doc.isObject())
        {
            // 直接返回设置对象，不包装在response结构中
            return doc.object();
        }
        else
        {
            // 返回空对象表示无效格式，前端会使用默认设置
            return QJsonObject();
        }
    }
    else
    {
        // 如果没有找到设置，返回空对象，让前端使用默认设置
        return QJsonObject();
    }
}

QJsonObject DatabaseApi::setAppSettings(const QString &settings)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    // 验证设置是否为有效的 JSON
    QJsonDocument doc = QJsonDocument::fromJson(settings.toUtf8());
    if (doc.isNull() || !doc.isObject())
    {
        result["success"] = false;
        result["error"] = "Invalid settings format";
        return result;
    }

    query.prepare("INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('app_settings', ?, CURRENT_TIMESTAMP)");
    query.addBindValue(settings);

    if (!query.exec())
    {
        qDebug() << "Error: Failed to save app settings:" << query.lastError().text();
        result["success"] = false;
        result["error"] = query.lastError().text();
        return result;
    }

    result["success"] = true;
    return result;
}

// 异步回调风格实现
void DatabaseApi::getAppSettings(QJSValue callback)
{
    QJsonObject result = this->getAppSettings();
    callWithJson(callback, result);
}

void DatabaseApi::setAppSettings(const QString &settings, QJSValue callback)
{
    QJsonObject result = this->setAppSettings(settings);
    callWithJson(callback, result);
}

QJsonObject DatabaseApi::getLlmSettings()
{
    QJsonObject result;
    QSqlQuery query(m_db);

    query.prepare("SELECT value FROM settings WHERE key = 'llm_settings'");

    if (query.exec() && query.next())
    {
        QString settingsJson = query.value("value").toString();
        QJsonDocument doc = QJsonDocument::fromJson(settingsJson.toUtf8());
        if (!doc.isNull() && doc.isObject())
        {
            result["success"] = true;
            result["settings"] = doc.object();
        }
        else
        {
            result["success"] = false;
            result["error"] = "Invalid settings format";
        }
    }
    else
    {
        // 如果没有找到设置，返回空，让前端使用默认设置
        result["success"] = true;
        result["settings"] = QJsonObject();
    }

    return result;
}

QJsonObject DatabaseApi::setLlmSettings(const QString &settings)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    // 验证设置是否为有效的 JSON
    QJsonDocument doc = QJsonDocument::fromJson(settings.toUtf8());
    if (doc.isNull() || !doc.isObject())
    {
        result["success"] = false;
        result["error"] = "Invalid settings format";
        return result;
    }

    query.prepare("INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES ('llm_settings', ?, CURRENT_TIMESTAMP)");
    query.addBindValue(settings);

    if (!query.exec())
    {
        qDebug() << "Error: Failed to save LLM settings:" << query.lastError().text();
        result["success"] = false;
        result["error"] = query.lastError().text();
        return result;
    }

    result["success"] = true;
    return result;
}

void DatabaseApi::getLlmSettings(QJSValue callback)
{
    QJsonObject result = this->getLlmSettings();
    callWithJson(callback, result);
}

void DatabaseApi::setLlmSettings(const QString &settings, QJSValue callback)
{
    QJsonObject result = this->setLlmSettings(settings);
    callWithJson(callback, result);
}

// 旧的getImage方法已被getImageAsBlob替代，保留此方法用于向后兼容
QJsonObject DatabaseApi::getImage(int image_id)
{
    // 直接调用新的getImageAsBlob方法
    return this->getImageAsBlob(image_id);
}

// 旧的deleteDocumentImages方法已被新的引用计数系统替代
QJsonObject DatabaseApi::deleteDocumentImages(int document_id)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    try
    {
        // 移除该文档对所有图片的引用
        query.prepare("DELETE FROM image_references WHERE document_id = ?");
        query.addBindValue(document_id);

        if (!query.exec())
        {
            throw query.lastError();
        }

        // 删除无引用的图片
        QJsonObject cleanupResult = this->deleteUnreferencedImages();
        if (!cleanupResult["success"].toBool())
        {
            qWarning() << "Failed to cleanup unreferenced images:" << cleanupResult["error"].toString();
        }

        result["success"] = true;
        result["removed_references"] = query.numRowsAffected();
    }
    catch (const QSqlError &error)
    {
        qDebug() << "Error: Failed to delete document images:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

// 旧的saveImage方法已被saveImageFromData替代，保留用于向后兼容
void DatabaseApi::saveImage(const QVariant &document_id, const QString &imageData, const QString &mimeType, QJSValue callback)
{
    int docId = variantToIntSafe(document_id);

    // 使用新的saveImageFromData方法
    QJsonObject result = this->saveImageFromData(imageData, mimeType, "");

    // 如果成功，添加图片引用
    if (result["success"].toBool() && result.contains("id"))
    {
        int imageId = result["id"].toInt();
        QJsonObject refResult = this->addImageReference(imageId, docId);
        if (!refResult["success"].toBool())
        {
            qWarning() << "Failed to add image reference:" << refResult["error"].toString();
        }
    }

    callWithJson(callback, result);
}

// 旧的saveImage同步方法已被新的引用计数系统替代
QJsonObject DatabaseApi::saveImage(int document_id, const QString &imageData, const QString &mimeType)
{
    // 使用新的saveImageFromData方法
    QJsonObject result = this->saveImageFromData(imageData, mimeType, "");

    // 如果成功，添加图片引用
    if (result["success"].toBool() && result.contains("id"))
    {
        int imageId = result["id"].toInt();
        QJsonObject refResult = this->addImageReference(imageId, document_id);
        if (!refResult["success"].toBool())
        {
            qWarning() << "Failed to add image reference:" << refResult["error"].toString();
        }
    }

    return result;
}

// 旧的checkImageReferences方法已被新的引用计数系统替代
QJsonObject DatabaseApi::checkImageReferences(int image_id, int excludeDocumentId)
{
    // 使用新的getImageReferences方法
    QJsonObject result = this->getImageReferences(image_id);

    if (result["success"].toBool())
    {
        QJsonArray references = result["references"].toArray();
        QJsonArray filteredReferences;

        // 过滤掉排除的文档ID
        for (const QJsonValue &ref : references)
        {
            QJsonObject refObj = ref.toObject();
            int docId = refObj["document_id"].toInt();

            if (excludeDocumentId == -1 || docId != excludeDocumentId)
            {
                filteredReferences.append(ref);
            }
        }

        result["referencingDocuments"] = filteredReferences;
        result["hasReferences"] = filteredReferences.size() > 0;
    }

    return result;
}

// 旧的deleteImage方法已被新的引用计数系统替代
QJsonObject DatabaseApi::deleteImage(int image_id, int currentDocumentId)
{
    QJsonObject result;

    try
    {
        // 移除当前文档对该图片的引用
        QJsonObject removeResult = this->removeImageReference(image_id, currentDocumentId);
        if (!removeResult["success"].toBool())
        {
            throw QSqlError("Failed to remove image reference", removeResult["error"].toString());
        }

        // 清理无引用的图片
        QJsonObject cleanupResult = this->deleteUnreferencedImages();
        if (!cleanupResult["success"].toBool())
        {
            qWarning() << "Failed to cleanup unreferenced images:" << cleanupResult["error"].toString();
        }

        result["success"] = true;
        result["removed_references"] = removeResult["affected_rows"].toInt();
        result["deleted_count"] = cleanupResult["deleted_count"].toInt();
    }
    catch (const QSqlError &error)
    {
        qDebug() << "Error: Failed to delete image:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

void DatabaseApi::checkImageReferences(const QVariant &image_id, QJSValue callback)
{
    int imgId = variantToIntSafe(image_id);
    QJsonObject result = this->checkImageReferences(imgId);
    callWithJson(callback, result);
}

void DatabaseApi::deleteImage(const QVariant &image_id, const QVariant &current_document_id, QJSValue callback)
{
    int imgId = variantToIntSafe(image_id);
    int currentDocId = variantToIntSafe(current_document_id);
    QJsonObject result = this->deleteImage(imgId, currentDocId);
    callWithJson(callback, result);
}

// Conversation 相关方法实现
QJsonObject DatabaseApi::createConversation(int document_id, const QString &title, const QString &messages, const QString &prompt)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 验证 messages 是否为有效的 JSON
        QJsonDocument doc = QJsonDocument::fromJson(messages.toUtf8());
        if (doc.isNull() || !doc.isArray())
        {
            throw QSqlError("Invalid JSON", "Messages must be a valid JSON array");
        }

        // 验证document_id是否存在（如果提供了，-1表示不关联）
        if (document_id > 0)
        {
            query.prepare("SELECT COUNT(*) FROM documents WHERE id = ?");
            query.addBindValue(document_id);
            if (!query.exec() || !query.next() || query.value(0).toInt() == 0)
            {
                throw QSqlError("Invalid document_id", "Document does not exist");
            }
        }

        // 创建对话
        if (document_id > 0)
        {
            query.prepare("INSERT INTO conversations (document_id, title, messages, prompt) VALUES (?, ?, ?, ?)");
            query.addBindValue(document_id);
            query.addBindValue(title);
            query.addBindValue(messages);
            query.addBindValue(prompt);
        }
        else
        {
            query.prepare("INSERT INTO conversations (title, messages, prompt) VALUES (?, ?, ?)");
            query.addBindValue(title);
            query.addBindValue(messages);
            query.addBindValue(prompt);
        }

        if (!query.exec())
        {
            throw query.lastError();
        }

        m_db.commit();
        result["success"] = true;
        result["id"] = query.lastInsertId().toInt();
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "Error: Failed to create conversation:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::getConversation(int id)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    query.prepare("SELECT * FROM conversations WHERE id = ?");
    query.addBindValue(id);

    if (query.exec() && query.next())
    {
        result["success"] = true;
        result["id"] = toJsonIdSafe(query.value("id"));
        result["document_id"] = toJsonNullableIdSafe(query.value("document_id")); // document_id使用-1表示独立对话
        result["title"] = query.value("title").toString();
        result["messages"] = query.value("messages").toString();
        result["prompt"] = query.value("prompt").toString();
        result["created_at"] = query.value("created_at").toString();
        result["updated_at"] = query.value("updated_at").toString();
    }
    else
    {
        result["success"] = false;
        result["error"] = "conversation not found";
    }

    return result;
}

QJsonObject DatabaseApi::updateConversation(int id, const QString &title, const QString &messages, const QString &prompt)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 验证 messages 是否为有效的 JSON
        QJsonDocument doc = QJsonDocument::fromJson(messages.toUtf8());
        if (doc.isNull() || !doc.isArray())
        {
            throw QSqlError("Invalid JSON", "Messages must be a valid JSON array");
        }

        // 更新对话
        query.prepare("UPDATE conversations SET title = ?, messages = ?, prompt = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        query.addBindValue(title);
        query.addBindValue(messages);
        query.addBindValue(prompt);
        query.addBindValue(id);

        if (!query.exec())
        {
            throw query.lastError();
        }

        if (query.numRowsAffected() == 0)
        {
            throw QSqlError("Not found", "Conversation does not exist");
        }

        m_db.commit();
        result["success"] = true;
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "Error: Failed to update conversation:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

QJsonObject DatabaseApi::deleteConversation(int id)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    query.prepare("DELETE FROM conversations WHERE id = ?");
    query.addBindValue(id);

    if (!query.exec())
    {
        result["success"] = false;
        result["error"] = query.lastError().text();
        return result;
    }

    if (query.numRowsAffected() == 0)
    {
        result["success"] = false;
        result["error"] = "conversation not found";
        return result;
    }

    result["success"] = true;
    return result;
}

QJsonObject DatabaseApi::listConversations(int document_id)
{
    QJsonObject result;
    QJsonArray conversations;
    QSqlQuery query(m_db);

    query.prepare("SELECT * FROM conversations WHERE document_id = ? ORDER BY created_at DESC");
    query.addBindValue(document_id);

    if (!query.exec())
    {
        qCWarning(databaseApi) << "Failed to execute listConversations query for document" << document_id << ":" << query.lastError().text();
        result["success"] = false;
        result["error"] = query.lastError().text();
        // 即使查询失败，也要返回空数组而不是undefined
        result["conversations"] = QJsonArray();
        return result;
    }

    qCInfo(databaseApi) << "Executing listConversations query for document:" << document_id;

    while (query.next())
    {
        QJsonObject conv;
        conv["id"] = toJsonIdSafe(query.value("id"));
        conv["document_id"] = toJsonNullableIdSafe(query.value("document_id")); // document_id使用-1表示独立对话
        conv["title"] = query.value("title").toString();
        conv["messages"] = query.value("messages").toString();
        conv["prompt"] = query.value("prompt").toString();
        conv["created_at"] = query.value("created_at").toString();
        conv["updated_at"] = query.value("updated_at").toString();
        conversations.append(conv);
    }

    qCInfo(databaseApi) << "Found" << conversations.size() << "conversations for document" << document_id;

    result["success"] = true;
    result["conversations"] = conversations;
    return result;
}

QJsonObject DatabaseApi::listAllConversations()
{
    QJsonObject result;
    QJsonArray conversations;
    QSqlQuery query(m_db);

    query.prepare("SELECT * FROM conversations ORDER BY created_at DESC");

    if (!query.exec())
    {
        qCWarning(databaseApi) << "Failed to execute listAllConversations query:" << query.lastError().text();
        result["success"] = false;
        result["error"] = query.lastError().text();
        // 即使查询失败，也要返回空数组而不是undefined
        result["conversations"] = QJsonArray();
        return result;
    }

    qCInfo(databaseApi) << "Executing listAllConversations query...";

    while (query.next())
    {
        QJsonObject conv;
        conv["id"] = toJsonIdSafe(query.value("id"));
        conv["document_id"] = toJsonNullableIdSafe(query.value("document_id")); // document_id使用-1表示独立对话
        conv["title"] = query.value("title").toString();
        conv["messages"] = query.value("messages").toString();
        conv["prompt"] = query.value("prompt").toString();
        conv["created_at"] = query.value("created_at").toString();
        conv["updated_at"] = query.value("updated_at").toString();
        conversations.append(conv);
    }

    qCInfo(databaseApi) << "Found" << conversations.size() << "total conversations";

    result["success"] = true;
    result["conversations"] = conversations;
    return result;
}

QJsonObject DatabaseApi::listChatConversations()
{
    QJsonObject result;
    QJsonArray conversations;
    QSqlQuery query(m_db);

    // 使用更明确的查询条件，包含所有可能的独立对话情况
    query.prepare("SELECT * FROM conversations WHERE document_id IS NULL OR document_id < 0 ORDER BY created_at DESC");

    if (!query.exec())
    {
        qCWarning(databaseApi) << "Failed to execute listChatConversations query:" << query.lastError().text();
        result["success"] = false;
        result["error"] = query.lastError().text();
        // 即使查询失败，也要返回空数组而不是undefined
        result["conversations"] = QJsonArray();
        return result;
    }

    qCInfo(databaseApi) << "Executing listChatConversations query...";

    while (query.next())
    {
        QJsonObject conv;
        conv["id"] = toJsonIdSafe(query.value("id"));
        conv["document_id"] = toJsonNullableIdSafe(query.value("document_id")); // document_id使用-1表示独立对话
        conv["title"] = query.value("title").toString();
        conv["messages"] = query.value("messages").toString();
        conv["prompt"] = query.value("prompt").toString();
        conv["created_at"] = query.value("created_at").toString();
        conv["updated_at"] = query.value("updated_at").toString();
        conversations.append(conv);
    }

    qCInfo(databaseApi) << "Found" << conversations.size() << "independent conversations";

    result["success"] = true;
    result["conversations"] = conversations;
    return result;
}

// 异步回调风格实现
void DatabaseApi::createConversation(const QVariant &document_id, const QString &title, const QString &messages, const QString &prompt, QJSValue callback)
{
    int docId = variantToIntSafe(document_id);
    QJsonObject result = this->createConversation(docId, title, messages, prompt);
    callWithJson(callback, result);
}

void DatabaseApi::getConversation(const QVariant &id, QJSValue callback)
{
    int convId = variantToIntSafe(id);
    QJsonObject result = this->getConversation(convId);
    callWithJson(callback, result);
}

void DatabaseApi::updateConversation(const QVariant &id, const QString &title, const QString &messages, const QString &prompt, QJSValue callback)
{
    int convId = variantToIntSafe(id);
    QJsonObject result = this->updateConversation(convId, title, messages, prompt);
    callWithJson(callback, result);
}

void DatabaseApi::deleteConversation(const QVariant &id, QJSValue callback)
{
    int convId = variantToIntSafe(id);
    QJsonObject result = this->deleteConversation(convId);
    callWithJson(callback, result);
}

void DatabaseApi::listConversations(const QVariant &document_id, QJSValue callback)
{
    int docId = variantToIntSafe(document_id);
    QJsonObject result = this->listConversations(docId);
    callWithJson(callback, result);
}

void DatabaseApi::listAllConversations(QJSValue callback)
{
    QJsonObject result = this->listAllConversations();
    callWithJson(callback, result);
}

void DatabaseApi::listChatConversations(QJSValue callback)
{
    QJsonObject result = this->listChatConversations();
    callWithJson(callback, result);
}

QJsonObject DatabaseApi::createSnapshot(int document_id, const QString &snapshot_name)
{
    QJsonObject result;
    QSqlQuery query(m_db);
    m_db.transaction();

    try
    {
        // 1. 获取当前文档的 content 和 snapshot
        query.prepare("SELECT content, snapshot FROM documents WHERE id = ?");
        query.addBindValue(document_id);

        if (!query.exec() || !query.next())
        {
            throw QSqlError("Not found", "Document does not exist");
        }

        QString content = query.value("content").toString();
        QString snapshotJson = query.value("snapshot").toString();

        // 2. 解析现有的快照数据
        QJsonArray snapshots;
        if (!snapshotJson.isEmpty() && snapshotJson != "null")
        {
            QJsonDocument doc = QJsonDocument::fromJson(snapshotJson.toUtf8());
            if (!doc.isNull() && doc.isArray())
            {
                snapshots = doc.array();
            }
        }

        // 3. 创建新的快照对象
        QJsonObject newSnapshot;
        newSnapshot["name"] = snapshot_name;
        newSnapshot["content"] = content;
        newSnapshot["created_at"] = QDateTime::currentDateTime().toString(Qt::ISODate);

        // 4. 将新快照插入到数组开头
        snapshots.prepend(newSnapshot);

        // 5. 更新数据库
        QJsonDocument newSnapshotDoc(snapshots);
        query.prepare("UPDATE documents SET snapshot = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        query.addBindValue(QString::fromUtf8(newSnapshotDoc.toJson(QJsonDocument::Compact)));
        query.addBindValue(document_id);

        if (!query.exec())
        {
            throw query.lastError();
        }

        m_db.commit();
        result["success"] = true;
        result["snapshot"] = newSnapshot;
    }
    catch (const QSqlError &error)
    {
        m_db.rollback();
        qDebug() << "Error: Failed to create snapshot:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

void DatabaseApi::createSnapshot(int document_id, const QString &snapshot_name, QJSValue callback)
{
    QJsonObject result = this->createSnapshot(document_id, snapshot_name);
    callWithJson(callback, result);
}

// 新增：雪花ID生成器实现
quint64 DatabaseApi::generateSnowflakeId()
{
    QMutexLocker locker(&s_snowflakeMutex);

    // 获取当前时间戳（毫秒）
    quint64 timestamp = QDateTime::currentMSecsSinceEpoch();

    // 如果时间戳相同，增加序列号
    if (timestamp == s_lastTimestamp)
    {
        s_sequence = (s_sequence + 1) & 0xFFF; // 12位序列号
        if (s_sequence == 0)
        {
            // 序列号用完了，等待下一毫秒
            while (timestamp <= s_lastTimestamp)
            {
                timestamp = QDateTime::currentMSecsSinceEpoch();
            }
        }
    }
    else
    {
        s_sequence = 0;
    }

    s_lastTimestamp = timestamp;

    // 生成ID：时间戳（41位）+ 机器ID（10位）+ 序列号（12位）
    // 这里简化处理，机器ID使用线程ID的低10位
    quint64 machineId = (quint64)(QThread::currentThreadId()) & 0x3FF;
    quint64 snowflakeId = ((timestamp & 0x1FFFFFFFFFF) << 22) | (machineId << 12) | s_sequence;

    return snowflakeId;
}

// 新增：获取用户数据目录
QString DatabaseApi::getUserDataPath()
{
    QString dataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    return QDir(dataPath).filePath("user");
}

// 新增：获取图片存储目录
QString DatabaseApi::getImagesPath()
{
    return QDir(getUserDataPath()).filePath("images");
}

// 新增：获取数据库存储目录
QString DatabaseApi::getDatabasePath()
{
    return QDir(getUserDataPath()).filePath("db");
}

// ==================== 新的图片处理系统实现 ====================
// 重构版本：统一图片处理、引用计数、智能删除

// 从base64数据保存图片
QJsonObject DatabaseApi::saveImageFromData(const QString &imageData, const QString &mimeType, const QString &originalUrl)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    qDebug() << "[IMAGE] Starting saveImageFromData";
    qDebug() << "[IMAGE] MIME type:" << mimeType;
    qDebug() << "[IMAGE] Original URL:" << originalUrl;
    qDebug() << "[IMAGE] Base64 data length:" << imageData.length();

    try
    {
        // 将base64字符串转换为二进制数据
        QByteArray binaryData = QByteArray::fromBase64(imageData.toUtf8());
        if (binaryData.isEmpty())
        {
            qDebug() << "[IMAGE] Error: Failed to convert base64 to binary data";
            throw QSqlError("Invalid base64 data", "Failed to convert base64 to binary");
        }

        qDebug() << "[IMAGE] Binary data size:" << binaryData.size() << "bytes";

        // 计算文件哈希值，用于去重
        QString hash = QString(QCryptographicHash::hash(binaryData, QCryptographicHash::Sha256).toHex());
        qDebug() << "[IMAGE] File hash:" << hash;

        // 检查是否已存在相同的图片
        query.prepare("SELECT id, file_path FROM images WHERE hash = ?");
        query.addBindValue(hash);
        if (query.exec() && query.next())
        {
            // 图片已存在，返回现有图片ID
            qDebug() << "[IMAGE] Duplicate image found, ID:" << query.value("id").toInt();
            result["success"] = true;
            result["id"] = query.value("id").toInt();
            result["file_path"] = query.value("file_path").toString();
            result["is_duplicate"] = true;
            return result;
        }

        // 生成文件名和路径
        quint64 snowflakeId = generateSnowflakeId();
        QString extension = getExtensionFromMimeType(mimeType);
        QString dateFolder = QDate::currentDate().toString("yyyy-MM-dd");
        QString fileName = QString::number(snowflakeId) + extension;
        QString relativePath = QDir(dateFolder).filePath(fileName);

        qDebug() << "[IMAGE] Generated snowflake ID:" << snowflakeId;
        qDebug() << "[IMAGE] File extension:" << extension;
        qDebug() << "[IMAGE] Date folder:" << dateFolder;
        qDebug() << "[IMAGE] File name:" << fileName;
        qDebug() << "[IMAGE] Relative path:" << relativePath;

        // 确保日期目录存在
        QString dateDirPath = QDir(getImagesPath()).filePath(dateFolder);
        qDebug() << "[IMAGE] Date directory path:" << dateDirPath;

        if (!ensureDirectoryExists(dateDirPath))
        {
            qDebug() << "[IMAGE] Error: Failed to create date directory:" << dateDirPath;
            throw QSqlError("Directory creation failed", "Failed to create image directory");
        }
        qDebug() << "[IMAGE] Date directory created/exists";

        // 保存图片到文件系统
        QString fullPath = QDir(getImagesPath()).filePath(relativePath);
        qDebug() << "[IMAGE] Full file path:" << fullPath;

        QFile file(fullPath);
        if (!file.open(QIODevice::WriteOnly))
        {
            qDebug() << "[IMAGE] Error: Failed to open file for writing:" << fullPath;
            qDebug() << "[IMAGE] File error:" << file.errorString();
            throw QSqlError("File write failed", "Failed to open file for writing: " + file.errorString());
        }

        qint64 bytesWritten = file.write(binaryData);
        if (bytesWritten == -1)
        {
            qDebug() << "[IMAGE] Error: Failed to write image data to file";
            qDebug() << "[IMAGE] File error:" << file.errorString();
            file.close();
            throw QSqlError("File write failed", "Failed to write image data: " + file.errorString());
        }

        qDebug() << "[IMAGE] Successfully wrote" << bytesWritten << "bytes to file";
        file.close();

        // 获取图片尺寸信息
        QImageReader reader(fullPath);
        QSize imageSize = reader.size();
        int width = imageSize.isValid() ? imageSize.width() : 0;
        int height = imageSize.isValid() ? imageSize.height() : 0;

        qDebug() << "[IMAGE] Image dimensions:" << width << "x" << height;
        if (!imageSize.isValid())
        {
            qDebug() << "[IMAGE] Warning: Could not read image dimensions, error:" << reader.errorString();
        }

        // 在数据库中记录图片信息
        qDebug() << "[IMAGE] Inserting image record into database";
        query.prepare("INSERT INTO images (file_path, original_url, mime_type, file_size, width, height, hash) "
                      "VALUES (?, ?, ?, ?, ?, ?, ?)");
        query.addBindValue(relativePath);
        query.addBindValue(originalUrl);
        query.addBindValue(mimeType);
        query.addBindValue(binaryData.size());
        query.addBindValue(width);
        query.addBindValue(height);
        query.addBindValue(hash);

        if (!query.exec())
        {
            // 如果数据库插入失败，删除已保存的文件
            qDebug() << "[IMAGE] Error: Database insert failed:" << query.lastError().text();
            qDebug() << "[IMAGE] Removing saved file:" << fullPath;
            QFile::remove(fullPath);
            throw query.lastError();
        }

        int imageId = query.lastInsertId().toInt();
        qDebug() << "[IMAGE] Successfully saved image with ID:" << imageId;

        result["success"] = true;
        result["id"] = imageId;
        result["file_path"] = relativePath;
        result["width"] = width;
        result["height"] = height;
        result["file_size"] = binaryData.size();
        result["is_duplicate"] = false;
    }
    catch (const QSqlError &error)
    {
        qDebug() << "[IMAGE] Error: Failed to save image from data:" << error.text();
        qDebug() << "[IMAGE] Error type:" << error.type();
        qDebug() << "[IMAGE] Database error:" << error.databaseText();
        qDebug() << "[IMAGE] Driver error:" << error.driverText();
        result["success"] = false;
        result["error"] = error.text();
    }
    catch (const std::exception &e)
    {
        qDebug() << "[IMAGE] Error: Exception occurred:" << e.what();
        result["success"] = false;
        result["error"] = QString("Exception: %1").arg(e.what());
    }
    catch (...)
    {
        qDebug() << "[IMAGE] Error: Unknown exception occurred";
        result["success"] = false;
        result["error"] = "Unknown error occurred while saving image";
    }

    qDebug() << "[IMAGE] saveImageFromData completed, success:" << result["success"].toBool();
    return result;
}

// 从文件路径保存图片
QJsonObject DatabaseApi::saveImageFromFile(const QString &filePath, const QString &originalUrl)
{
    QJsonObject result;

    try
    {
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly))
        {
            throw QSqlError("File read failed", "Failed to open file for reading");
        }

        QByteArray fileData = file.readAll();
        file.close();

        if (fileData.isEmpty())
        {
            throw QSqlError("Empty file", "File is empty or could not be read");
        }

        // 检测MIME类型
        QString mimeType = "image/jpeg"; // 默认值
        QImageReader reader(filePath);
        QString format = reader.format();
        if (!format.isEmpty())
        {
            mimeType = QString("image/%1").arg(format.toLower());
        }

        // 转换为base64并调用saveImageFromData
        QString base64Data = fileData.toBase64();
        result = saveImageFromData(base64Data, mimeType, originalUrl);
    }
    catch (const QSqlError &error)
    {
        qDebug() << "Error: Failed to save image from file:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

// 获取图片作为blob数据
QJsonObject DatabaseApi::getImageAsBlob(int imageId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    query.prepare("SELECT file_path, mime_type FROM images WHERE id = ?");
    query.addBindValue(imageId);

    if (query.exec() && query.next())
    {
        QString relativePath = query.value("file_path").toString();
        QString mimeType = query.value("mime_type").toString();

        // 构造完整路径
        QString fullPath = QDir(getImagesPath()).filePath(relativePath);

        // 检查文件是否存在
        if (QFile::exists(fullPath))
        {
            // 读取文件并转换为base64
            QFile file(fullPath);
            if (file.open(QIODevice::ReadOnly))
            {
                QByteArray fileData = file.readAll();
                QString base64Data = fileData.toBase64();
                file.close();

                result["success"] = true;
                result["data"] = base64Data;
                result["mime_type"] = mimeType;
                result["file_path"] = relativePath;
            }
            else
            {
                result["success"] = false;
                result["error"] = "failed to read image file";
            }
        }
        else
        {
            result["success"] = false;
            result["error"] = "image file not found";
        }
    }
    else
    {
        result["success"] = false;
        result["error"] = "image record not found";
    }

    return result;
}

// 添加图片引用
QJsonObject DatabaseApi::addImageReference(int imageId, int documentId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    // 使用INSERT OR IGNORE避免重复引用
    query.prepare("INSERT OR IGNORE INTO image_references (image_id, document_id) VALUES (?, ?)");
    query.addBindValue(imageId);
    query.addBindValue(documentId);

    if (query.exec())
    {
        result["success"] = true;
        result["affected_rows"] = query.numRowsAffected();
    }
    else
    {
        qDebug() << "Error: Failed to add image reference:" << query.lastError().text();
        result["success"] = false;
        result["error"] = query.lastError().text();
    }

    return result;
}

// 移除图片引用
QJsonObject DatabaseApi::removeImageReference(int imageId, int documentId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    query.prepare("DELETE FROM image_references WHERE image_id = ? AND document_id = ?");
    query.addBindValue(imageId);
    query.addBindValue(documentId);

    if (query.exec())
    {
        result["success"] = true;
        result["affected_rows"] = query.numRowsAffected();
    }
    else
    {
        qDebug() << "Error: Failed to remove image reference:" << query.lastError().text();
        result["success"] = false;
        result["error"] = query.lastError().text();
    }

    return result;
}

// 删除无引用的图片
QJsonObject DatabaseApi::deleteUnreferencedImages()
{
    QJsonObject result;
    QSqlQuery query(m_db);
    QStringList deletedFiles;
    int deletedCount = 0;

    try
    {
        // 查找所有无引用的图片
        query.prepare(R"(
            SELECT i.id, i.file_path
            FROM images i
            LEFT JOIN image_references ir ON i.id = ir.image_id
            WHERE ir.image_id IS NULL
        )");

        if (!query.exec())
        {
            throw query.lastError();
        }

        // 收集要删除的图片信息
        QList<QPair<int, QString>> imagesToDelete;
        while (query.next())
        {
            int imageId = query.value("id").toInt();
            QString filePath = query.value("file_path").toString();
            imagesToDelete.append(qMakePair(imageId, filePath));
        }

        // 删除文件和数据库记录
        for (const auto &imageInfo : imagesToDelete)
        {
            int imageId = imageInfo.first;
            QString relativePath = imageInfo.second;
            QString fullPath = QDir(getImagesPath()).filePath(relativePath);

            // 删除文件
            if (QFile::exists(fullPath))
            {
                if (QFile::remove(fullPath))
                {
                    deletedFiles.append(relativePath);
                }
                else
                {
                    qWarning() << "Failed to delete image file:" << fullPath;
                }
            }

            // 删除数据库记录
            QSqlQuery deleteQuery(m_db);
            deleteQuery.prepare("DELETE FROM images WHERE id = ?");
            deleteQuery.addBindValue(imageId);
            if (deleteQuery.exec())
            {
                deletedCount++;
            }
            else
            {
                qWarning() << "Failed to delete image record:" << deleteQuery.lastError().text();
            }
        }

        result["success"] = true;
        result["deleted_count"] = deletedCount;
        result["deleted_files"] = QJsonArray::fromStringList(deletedFiles);
    }
    catch (const QSqlError &error)
    {
        qDebug() << "Error: Failed to delete unreferenced images:" << error.text();
        result["success"] = false;
        result["error"] = error.text();
    }

    return result;
}

// 获取图片引用信息
QJsonObject DatabaseApi::getImageReferences(int imageId)
{
    QJsonObject result;
    QSqlQuery query(m_db);

    query.prepare(R"(
        SELECT ir.document_id, d.title
        FROM image_references ir
        LEFT JOIN documents d ON ir.document_id = d.id
        WHERE ir.image_id = ?
    )");
    query.addBindValue(imageId);

    if (query.exec())
    {
        QJsonArray references;
        while (query.next())
        {
            QJsonObject ref;
            ref["document_id"] = query.value("document_id").toInt();
            ref["document_title"] = query.value("title").toString();
            references.append(ref);
        }

        result["success"] = true;
        result["references"] = references;
        result["reference_count"] = references.size();
    }
    else
    {
        qDebug() << "Error: Failed to get image references:" << query.lastError().text();
        result["success"] = false;
        result["error"] = query.lastError().text();
    }

    return result;
}

// ==================== JavaScript回调版本的方法实现 ====================

void DatabaseApi::saveImageFromData(const QString &imageData, const QString &mimeType, const QString &originalUrl, QJSValue callback)
{
    QJsonObject result = this->saveImageFromData(imageData, mimeType, originalUrl);
    callWithJson(callback, result);
}

void DatabaseApi::saveImageFromFile(const QString &filePath, const QString &originalUrl, QJSValue callback)
{
    QJsonObject result = this->saveImageFromFile(filePath, originalUrl);
    callWithJson(callback, result);
}

void DatabaseApi::getImageAsBlob(const QVariant &imageId, QJSValue callback)
{
    int imgId = variantToIntSafe(imageId);
    QJsonObject result = this->getImageAsBlob(imgId);
    callWithJson(callback, result);
}

void DatabaseApi::getImage(const QVariant &image_id, QJSValue callback)
{
    int imgId = variantToIntSafe(image_id);
    QJsonObject result = this->getImage(imgId);
    callWithJson(callback, result);
}

void DatabaseApi::addImageReference(const QVariant &imageId, const QVariant &documentId, QJSValue callback)
{
    int imgId = variantToIntSafe(imageId);
    int docId = variantToIntSafe(documentId);
    QJsonObject result = this->addImageReference(imgId, docId);
    callWithJson(callback, result);
}

void DatabaseApi::removeImageReference(const QVariant &imageId, const QVariant &documentId, QJSValue callback)
{
    int imgId = variantToIntSafe(imageId);
    int docId = variantToIntSafe(documentId);
    QJsonObject result = this->removeImageReference(imgId, docId);
    callWithJson(callback, result);
}

void DatabaseApi::deleteUnreferencedImages(QJSValue callback)
{
    QJsonObject result = this->deleteUnreferencedImages();
    callWithJson(callback, result);
}

void DatabaseApi::getImageReferences(const QVariant &imageId, QJSValue callback)
{
    int imgId = variantToIntSafe(imageId);
    QJsonObject result = this->getImageReferences(imgId);
    callWithJson(callback, result);
}

void DatabaseApi::linkImageToDocument(const QVariant &imageId, const QVariant &documentId, QJSValue callback)
{
    qWarning() << "[DatabaseApi] linkImageToDocument was called";
    qWarning() << "[DatabaseApi] args: imageId=" << imageId << ", documentId=" << documentId;

    int imgId = variantToIntSafe(imageId);
    int docId = variantToIntSafe(documentId);

    qWarning() << "[DatabaseApi] args after conversion: imgId=" << imgId << ", docId=" << docId;

    QJsonObject result;

    try
    {
        // 直接添加图片引用，不依赖文档保存
        QJsonObject refResult = this->addImageReference(imgId, docId);

        if (refResult["success"].toBool())
        {
            result["success"] = true;
            result["message"] = QString("linked img %1 to doc %2").arg(imgId).arg(docId);
            qWarning() << "linked success:" << imgId << "→" << docId;
        }
        else
        {
            result["success"] = false;
            result["error"] = refResult["error"].toString();
            qWarning() << "linked faild:" << imgId << "→" << docId << "error:" << refResult["error"].toString();
        }
    }
    catch (const std::exception &e)
    {
        result["success"] = false;
        result["error"] = QString("pinca on link: %1").arg(e.what());
        qWarning() << "pinca on link:" << e.what();
    }

    callWithJson(callback, result);
}

// 新增：确保目录存在
bool DatabaseApi::ensureDirectoryExists(const QString &path)
{
    QDir dir(path);
    if (!dir.exists())
    {
        return dir.mkpath(".");
    }
    return true;
}

// 新增：根据MIME类型获取文件扩展名
QString DatabaseApi::getExtensionFromMimeType(const QString &mimeType)
{
    static QMap<QString, QString> mimeToExtension = {
        {"image/jpeg", ".jpg"},
        {"image/jpg", ".jpg"},
        {"image/png", ".png"},
        {"image/gif", ".gif"},
        {"image/webp", ".webp"},
        {"image/svg+xml", ".svg"},
        {"image/bmp", ".bmp"},
        {"image/x-icon", ".ico"},
        {"image/vnd.microsoft.icon", ".ico"}};

    return mimeToExtension.value(mimeType.toLower(), ".jpg"); // 默认使用.jpg
}

// 下载并保存图片 - 异步回调版本
void DatabaseApi::downloadAndSaveImage(const QVariant &document_id, const QString &imageUrl, QJSValue callback)
{
    int docId = variantToIntSafe(document_id);

    // 创建网络请求
    QUrl url(imageUrl);
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::UserAgentHeader, "InkCop/1.0");
    request.setRawHeader("Accept", "image/*");

    // 发送GET请求
    QNetworkReply *reply = m_networkManager->get(request);

    // 连接完成信号，使用lambda捕获参数
    connect(reply, &QNetworkReply::finished, [this, reply, docId, callback]() mutable
            { handleImageDownloadFinished(reply, docId, callback); });

    // 设置超时（10秒）
    QTimer::singleShot(10000, reply, [reply]()
                       {
        if (reply->isRunning()) {
            reply->abort();
        } });
}

// 下载并保存图片 - 同步版本
QJsonObject DatabaseApi::downloadAndSaveImage(int document_id, const QString &imageUrl)
{
    QJsonObject result;

    try
    {
        // 创建网络请求
        QUrl url(imageUrl);
        QNetworkRequest request(url);
        request.setHeader(QNetworkRequest::UserAgentHeader, "InkCop/1.0");
        request.setRawHeader("Accept", "image/*");

        // 发送同步请求
        QNetworkReply *reply = m_networkManager->get(request);

        // 等待请求完成
        QEventLoop loop;
        connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);

        // 设置超时
        QTimer timeoutTimer;
        timeoutTimer.setSingleShot(true);
        connect(&timeoutTimer, &QTimer::timeout, [&loop, reply]()
                {
            reply->abort();
            loop.quit(); });
        timeoutTimer.start(10000); // 10秒超时

        loop.exec();

        if (reply->error() != QNetworkReply::NoError)
        {
            result["success"] = false;
            result["error"] = QString("network request failed: %1").arg(reply->errorString());
            reply->deleteLater();
            return result;
        }

        // 获取响应数据
        QByteArray imageData = reply->readAll();
        if (imageData.isEmpty())
        {
            result["success"] = false;
            result["error"] = "downloaded image data is empty";
            reply->deleteLater();
            return result;
        }

        // 获取Content-Type
        QString contentType = reply->header(QNetworkRequest::ContentTypeHeader).toString();
        if (contentType.isEmpty() || !contentType.startsWith("image/"))
        {
            // 尝试从URL推断类型
            QString url = imageUrl.toLower();
            if (url.endsWith(".jpg") || url.endsWith(".jpeg"))
            {
                contentType = "image/jpeg";
            }
            else if (url.endsWith(".png"))
            {
                contentType = "image/png";
            }
            else if (url.endsWith(".gif"))
            {
                contentType = "image/gif";
            }
            else if (url.endsWith(".webp"))
            {
                contentType = "image/webp";
            }
            else
            {
                contentType = "image/jpeg"; // 默认
            }
        }

        reply->deleteLater();

        // 将二进制数据转换为base64并使用新的保存方法
        QString base64Data = imageData.toBase64();
        QJsonObject saveResult = saveImageFromData(base64Data, contentType, imageUrl);

        // 如果成功，添加图片引用
        if (saveResult["success"].toBool() && saveResult.contains("id"))
        {
            int imageId = saveResult["id"].toInt();
            QJsonObject refResult = addImageReference(imageId, document_id);
            if (!refResult["success"].toBool())
            {
                qWarning() << "Failed to add image reference:" << refResult["error"].toString();
            }
        }

        return saveResult;
    }
    catch (const std::exception &e)
    {
        result["success"] = false;
        result["error"] = QString("exception occurred while downloading image: %1").arg(e.what());
        return result;
    }
}

// 处理图片下载完成
void DatabaseApi::handleImageDownloadFinished(QNetworkReply *reply, int document_id, QJSValue callback)
{
    QJsonObject result;

    try
    {
        if (reply->error() != QNetworkReply::NoError)
        {
            result["success"] = false;
            result["error"] = QString("network request failed: %1").arg(reply->errorString());
        }
        else
        {
            // 获取响应数据
            QByteArray imageData = reply->readAll();
            if (imageData.isEmpty())
            {
                result["success"] = false;
                result["error"] = "downloaded image data is empty";
            }
            else
            {
                // 获取Content-Type
                QString contentType = reply->header(QNetworkRequest::ContentTypeHeader).toString();
                if (contentType.isEmpty() || !contentType.startsWith("image/"))
                {
                    // 尝试从URL推断类型
                    QString url = reply->url().toString().toLower();
                    if (url.endsWith(".jpg") || url.endsWith(".jpeg"))
                    {
                        contentType = "image/jpeg";
                    }
                    else if (url.endsWith(".png"))
                    {
                        contentType = "image/png";
                    }
                    else if (url.endsWith(".gif"))
                    {
                        contentType = "image/gif";
                    }
                    else if (url.endsWith(".webp"))
                    {
                        contentType = "image/webp";
                    }
                    else
                    {
                        contentType = "image/jpeg"; // 默认
                    }
                }

                // 将二进制数据转换为base64并使用新的保存方法
                QString base64Data = imageData.toBase64();
                QString originalUrl = reply->url().toString();
                result = saveImageFromData(base64Data, contentType, originalUrl);

                // 如果成功，添加图片引用
                if (result["success"].toBool() && result.contains("id"))
                {
                    int imageId = result["id"].toInt();
                    QJsonObject refResult = addImageReference(imageId, document_id);
                    if (!refResult["success"].toBool())
                    {
                        qWarning() << "Failed to add image reference:" << refResult["error"].toString();
                    }
                }
            }
        }
    }
    catch (const std::exception &e)
    {
        result["success"] = false;
        result["error"] = QString("exception occurred while processing downloaded image: %1").arg(e.what());
    }

    // 调用回调函数
    callWithJson(callback, result);

    // 清理
    reply->deleteLater();
}

// 中英文混合关键词拆分辅助方法
QStringList DatabaseApi::splitChineseEnglishKeywords(const QString &text)
{
    QStringList result;

    if (text.trimmed().isEmpty())
    {
        return result;
    }

    // 首先按照传统分隔符拆分（空格、逗号、分号）
    QStringList initialSplit = text.split(QRegularExpression("[\\s,;]+"), Qt::SkipEmptyParts);

    // 对每个片段进行中英文拆分
    for (const QString &segment : initialSplit)
    {
        QString trimmedSegment = segment.trimmed();
        if (trimmedSegment.isEmpty())
        {
            continue;
        }

        // 使用正则表达式匹配连续的中文字符或连续的英文数字字符
        QRegularExpression chineseEnglishPattern(R"(([\u4e00-\u9fff]+|[a-zA-Z0-9]+))");
        QRegularExpressionMatchIterator iterator = chineseEnglishPattern.globalMatch(trimmedSegment);

        bool hasMatches = false;
        while (iterator.hasNext())
        {
            QRegularExpressionMatch match = iterator.next();
            QString matchedText = match.captured(1);
            if (!matchedText.isEmpty())
            {
                result.append(matchedText);
                hasMatches = true;
            }
        }

        // 如果没有匹配到中英文字符，说明可能包含特殊字符，直接添加原始片段
        if (!hasMatches)
        {
            result.append(trimmedSegment);
        }
    }

    // 去重并保持顺序
    QStringList uniqueResult;
    for (const QString &keyword : result)
    {
        if (!uniqueResult.contains(keyword, Qt::CaseInsensitive))
        {
            uniqueResult.append(keyword);
        }
    }

    return uniqueResult;
}