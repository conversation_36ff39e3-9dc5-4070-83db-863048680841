# 分离渲染配置指南

## 概述

新的渲染配置系统支持分离Qt应用和WebEngine的GPU设置，允许您：
- **Qt应用使用GPU加速** - 获得更好的UI性能（窗口、控件、动画等）
- **WebEngine使用软件渲染** - 避免网页内容的GPU渲染问题

## 配置文件结构

### [QT_APPLICATION] - Qt应用GPU设置
控制Qt应用本身的硬件加速，不影响WebEngine。

```ini
[QT_APPLICATION]
# Qt应用本身的GPU加速设置（不影响WebEngine）
enable_gpu_acceleration=true

# 是否使用OpenGL ES而不是Desktop OpenGL
use_opengl_es=false

# 是否启用Qt的硬件加速合成
enable_hardware_composition=true
```

### [WEBENGINE_RENDERING] - WebEngine渲染设置
专门控制网页内容的渲染方式，独立于Qt应用的GPU设置。

```ini
[WEBENGINE_RENDERING]
# 是否强制WebEngine使用软件渲染
force_software_rendering=true

# 以下选项仅在force_software_rendering=false时有效
enable_2d_canvas_acceleration=false
enable_webgl=false
enable_scroll_animation=false
```

## 推荐配置

### 最佳性能配置（推荐）
```ini
[QT_APPLICATION]
enable_gpu_acceleration=true
use_opengl_es=false
enable_hardware_composition=true

[WEBENGINE_RENDERING]
force_software_rendering=true
enable_2d_canvas_acceleration=false
enable_webgl=false
enable_scroll_animation=false
```

这个配置提供：
- ✅ Qt应用界面流畅（GPU加速）
- ✅ WebEngine稳定渲染（软件渲染）
- ✅ 避免大多数渲染问题

### 全软件渲染配置（最安全）
```ini
[QT_APPLICATION]
enable_gpu_acceleration=false

[WEBENGINE_RENDERING]
force_software_rendering=true
```

### 全GPU加速配置（高性能GPU）
```ini
[QT_APPLICATION]
enable_gpu_acceleration=true

[WEBENGINE_RENDERING]
force_software_rendering=false
enable_2d_canvas_acceleration=true
enable_webgl=true
enable_scroll_animation=true
```

## 技术实现

### 1. Qt应用GPU加速
通过设置QApplication属性控制：
- `Qt::AA_UseDesktopOpenGL` - 使用桌面OpenGL
- `Qt::AA_UseOpenGLES` - 使用OpenGL ES
- `Qt::AA_UseSoftwareOpenGL` - 使用软件OpenGL

### 2. WebEngine软件渲染
通过Chromium标志强制软件渲染：
```
--disable-gpu
--disable-gpu-sandbox
--disable-accelerated-2d-canvas
--disable-accelerated-video-decode
--disable-gpu-rasterization
```

### 3. 配置加载顺序
1. 读取配置文件设置
2. 设置Qt应用属性（在QApplication创建前）
3. 设置WebEngine环境变量
4. 应用QWebEngineSettings

## 故障排除

### Qt应用界面问题
如果遇到窗口、控件渲染问题：
```ini
[QT_APPLICATION]
enable_gpu_acceleration=false
```

### WebEngine网页问题
如果遇到网页内容渲染问题：
```ini
[WEBENGINE_RENDERING]
force_software_rendering=true
```

### 向后兼容
旧的`[TROUBLESHOOTING]`部分仍然支持，但建议迁移到新配置：
- `TROUBLESHOOTING/force_software_rendering` → `WEBENGINE_RENDERING/force_software_rendering`
- `GPU_RENDERING/*` → `WEBENGINE_RENDERING/*`

## 日志输出

启用详细日志查看配置效果：
```ini
[TROUBLESHOOTING]
enable_verbose_logging=true
```

查看日志输出：
```
🎮 Qt Application: Desktop OpenGL enabled for GPU acceleration
🔧 WebEngine software rendering: true
🔧 WebEngine Chromium flags: --disable-gpu --disable-gpu-sandbox...
```

## 注意事项

1. **重启应用** - 修改配置后必须重启应用程序
2. **GPU驱动** - 确保GPU驱动程序是最新版本
3. **性能监控** - 观察CPU和GPU使用率变化
4. **逐步调试** - 如有问题，先尝试全软件渲染，再逐步启用功能
