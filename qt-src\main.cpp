#include <QApplication>
#include <QWebEngineProfile>
#include <QStandardPaths>
#include "mainwindow.h"
#include <QCoreApplication>
#include <QIcon>
#include <QLoggingCategory>
#include <QFile>
#include <QSize>
#include <QDir>
#include <QTextStream>
#include <QSettings>

int main(int argc, char *argv[])
{
    // 从配置文件读取渲染设置
    QSettings config("qt-rendering.conf", QSettings::IniFormat);

    // 读取Qt应用GPU加速设置
    bool qtAppGpuAcceleration = config.value("QT_APPLICATION/enable_gpu_acceleration", true).toBool();
    bool useOpenGLES = config.value("QT_APPLICATION/use_opengl_es", false).toBool();
    bool enableHardwareComposition = config.value("QT_APPLICATION/enable_hardware_composition", true).toBool();

    // 读取WebEngine渲染设置
    bool webEngineForceSwRendering = config.value("WEBENGINE_RENDERING/force_software_rendering", true).toBool();

    // 构建WebEngine专用的Chromium标志
    QString webEngineFlags;

    if (webEngineForceSwRendering)
    {
        // 强制WebEngine使用软件渲染的标志
        webEngineFlags = "--disable-gpu "
                         "--disable-gpu-sandbox "
                         "--disable-software-rasterizer "
                         "--disable-accelerated-2d-canvas "
                         "--disable-accelerated-jpeg-decoding "
                         "--disable-accelerated-mjpeg-decode "
                         "--disable-accelerated-video-decode "
                         "--disable-gpu-rasterization "
                         "--disable-oop-rasterization "
                         "--disable-zero-copy "
                         "--num-raster-threads=1";
    }
    else
    {
        // 使用配置文件中的自定义标志（保持向后兼容）
        webEngineFlags = config.value("CHROMIUM_FLAGS/custom_flags",
                                      "--disable-gpu-sandbox "
                                      "--disable-software-rasterizer "
                                      "--disable-background-timer-throttling "
                                      "--disable-backgrounding-occluded-windows "
                                      "--disable-renderer-backgrounding "
                                      "--disable-features=TranslateUI "
                                      "--disable-ipc-flooding-protection "
                                      "--disable-accelerated-2d-canvas "
                                      "--disable-accelerated-jpeg-decoding "
                                      "--disable-accelerated-mjpeg-decode "
                                      "--disable-accelerated-video-decode "
                                      "--num-raster-threads=1")
                             .toString();
    }

    // 读取GPU线程设置（仅影响WebEngine）
    bool disableGpuThread = webEngineForceSwRendering || config.value("TROUBLESHOOTING/disable_gpu_thread", true).toBool();

    // 从配置文件读取网络访问设置
    bool disableWebSecurity = config.value("NETWORK_ACCESS/disable_web_security", true).toBool();
    bool allowRunningInsecureContent = config.value("NETWORK_ACCESS/allow_running_insecure_content", true).toBool();

    // 添加本地网络访问支持的Chromium标志
    QString networkFlags = " --disable-features=VizDisplayCompositor";
    if (disableWebSecurity)
    {
        networkFlags += " --disable-web-security";
    }
    if (allowRunningInsecureContent)
    {
        networkFlags += " --allow-running-insecure-content";
    }
    webEngineFlags += networkFlags;

    // 应用WebEngine设置
    qputenv("QTWEBENGINE_CHROMIUM_FLAGS", webEngineFlags.toUtf8());

    if (disableGpuThread)
    {
        qputenv("QTWEBENGINE_DISABLE_GPU_THREAD", "1");
    }

    // 创建QApplication之前设置Qt应用的GPU加速属性
    if (qtAppGpuAcceleration)
    {
        if (useOpenGLES)
        {
            QApplication::setAttribute(Qt::AA_UseOpenGLES, true);
            qDebug() << "🎮 Qt Application: OpenGL ES enabled for GPU acceleration";
        }
        else
        {
            QApplication::setAttribute(Qt::AA_UseDesktopOpenGL, true);
            qDebug() << "🎮 Qt Application: Desktop OpenGL enabled for GPU acceleration";
        }

        if (enableHardwareComposition)
        {
            QApplication::setAttribute(Qt::AA_CompressHighFrequencyEvents, true);
            qDebug() << "🎮 Qt Application: Hardware composition optimizations enabled";
        }
    }
    else
    {
        QApplication::setAttribute(Qt::AA_UseSoftwareOpenGL, true);
        qDebug() << "🎮 Qt Application: Software OpenGL fallback enabled";
    }

    qDebug() << "🔧 WebEngine Chromium flags:" << webEngineFlags;
    qDebug() << "🔧 WebEngine GPU thread disabled:" << disableGpuThread;
    qDebug() << "🔧 WebEngine software rendering:" << webEngineForceSwRendering;
    qDebug() << "🎮 Qt Application GPU acceleration:" << qtAppGpuAcceleration;

    // 设置日志过滤规则，优先从文件加载，否则使用内置规则
    QString logRules;

    // 尝试从配置文件加载日志规则
    QFile logFile("qt-logging.rules");
    if (logFile.exists() && logFile.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QTextStream in(&logFile);
        QString line;
        while (!in.atEnd())
        {
            line = in.readLine().trimmed();
            // 跳过注释和空行
            if (!line.isEmpty() && !line.startsWith('#'))
            {
                if (!logRules.isEmpty())
                {
                    logRules += "\n";
                }
                logRules += line;
            }
        }
        logFile.close();
        qDebug() << "📋 Loading Qt logging rules from file:" << logFile.fileName();
    }

    // 如果文件不存在或为空，使用内置规则
    if (logRules.isEmpty())
    {
        logRules = QStringLiteral(
            "inkcop.vector.info=true\n"
            "inkcop.vector.debug=true\n"
            "inkcop.vector.warning=true\n"
            "inkcop.vector.critical=true\n"
            "inkcop.debug=true\n"
            "qt.*.debug=false\n"
            "qt.text.font.db=false\n"
            "qt.text.font=false\n"
            "qt.core.qobject.connect=false\n"
            "qt.webenginecontext=false\n"
            "qt.webengine.view.debug=false\n"
            "*.debug=false\n"
            "*.warning=true\n"
            "*.critical=true\n");
        qDebug() << "📋 Using built-in Qt logging rules";
    }

    QLoggingCategory::setFilterRules(logRules);

    QApplication app(argc, argv);

// 输出构建信息
#ifdef QT_DEBUG
    qDebug() << "🔧 inkCop started (development mode)";
    qDebug() << "   - Will attempt to connect to development server (http://localhost:9000)";
    qDebug() << "   - If development server is not available, will fall back to embedded resources";
#else
    qDebug() << "🚀 inkCop started (production mode)";
    qDebug() << "   - Will directly load embedded web application resources";
#endif

    // --- 应用核心信息设置 ---
    // 下面的设置将决定配置、缓存和数据的存储路径
    // 例如，在Linux上路径会是: ~/.local/share/inkCop
    QCoreApplication::setOrganizationName(""); // Set to empty, simplify directory structure
    QCoreApplication::setOrganizationDomain("com.inkcop.app");
    QCoreApplication::setApplicationName("inkCop");
    QCoreApplication::setApplicationVersion("1.0.0");

    // --- GUI相关设置 ---
    // 尝试加载不同尺寸的图标，优先使用高分辨率图标
    QIcon appIcon;
    qDebug() << "Loading application icons...";

    if (QFile::exists(":/icons/icon-512x512.png"))
    {
        appIcon.addFile(":/icons/icon-512x512.png", QSize(512, 512));
        qDebug() << "✓ Loaded 512x512 icon";
    }
    else
    {
        qDebug() << "✗ 512x512 icon not found";
    }

    if (QFile::exists(":/icons/icon-256x256.png"))
    {
        appIcon.addFile(":/icons/icon-256x256.png", QSize(256, 256));
        qDebug() << "✓ Loaded 256x256 icon";
    }
    else
    {
        qDebug() << "✗ 256x256 icon not found";
    }

    if (QFile::exists(":/icons/favicon-32x32.png"))
    {
        appIcon.addFile(":/icons/favicon-32x32.png", QSize(32, 32));
        qDebug() << "✓ Loaded 32x32 icon";
    }
    else
    {
        qDebug() << "✗ 32x32 icon not found";
    }

    if (QFile::exists(":/logo.png"))
    {
        appIcon.addFile(":/logo.png");
        qDebug() << "✓ Loaded logo.png as fallback";
    }
    else
    {
        qDebug() << "✗ logo.png not found";
    }

    if (!appIcon.isNull())
    {
        app.setWindowIcon(appIcon);
        qDebug() << "✓ Application icon set successfully with" << appIcon.availableSizes().size() << "sizes";
        qDebug() << "Available icon sizes:" << appIcon.availableSizes();
    }
    else
    {
        qDebug() << "✗ Warning: No application icon found";
    }

    app.setApplicationDisplayName("inkCop"); // 设置显示名称

    // --- WebEngine 设置 ---
    QWebEngineProfile::defaultProfile()->setPersistentCookiesPolicy(
        QWebEngineProfile::ForcePersistentCookies);

    // 设置WebEngine缓存路径 - 确保Windows路径兼容性
    QString cachePath = QStandardPaths::writableLocation(QStandardPaths::CacheLocation);
    cachePath = QDir::toNativeSeparators(cachePath);
    QWebEngineProfile::defaultProfile()->setCachePath(cachePath);

    qDebug() << "WebEngine cache path:" << cachePath;

// 开发模式WebEngine优化
#ifdef DEV_MODE
    // 开发模式：启用更多缓存和优化
    QWebEngineProfile::defaultProfile()->setHttpCacheType(QWebEngineProfile::DiskHttpCache);
    QWebEngineProfile::defaultProfile()->setHttpCacheMaximumSize(100 * 1024 * 1024); // 100MB缓存

    // 设置开发模式的用户代理
    QWebEngineProfile::defaultProfile()->setHttpUserAgent("InkCop-Dev/1.0");

    qDebug() << "🔧 Development mode WebEngine optimization enabled";
#else
    // 生产模式：使用默认设置，但保持本地网络访问能力
    QWebEngineProfile::defaultProfile()->setHttpCacheType(QWebEngineProfile::MemoryHttpCache);
    QWebEngineProfile::defaultProfile()->setHttpCacheMaximumSize(50 * 1024 * 1024); // 50MB缓存

    // 设置生产模式的用户代理
    QWebEngineProfile::defaultProfile()->setHttpUserAgent("InkCop-Production/1.0");

    qDebug() << "🚀 Production mode WebEngine configuration enabled, supports local service access";
#endif

    // --- 启动主窗口 ---
    MainWindow window;
    window.show();

    return app.exec();
}