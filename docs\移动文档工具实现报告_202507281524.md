# 移动文档工具实现报告

**实现时间**: 2025-07-28 15:24  
**功能描述**: 新增AI工具函数，支持移动文档到指定文件夹，新移入的文档排在目标文件夹第一位

## 功能概述

实现了一个完整的文档移动功能，包括：
1. 后端API方法 `moveDocument`
2. 前端封装方法 `useSqlite().moveDocument`
3. AI工具函数 `move_document`
4. 完整的类型定义和错误处理

## 实现细节

### 1. 后端实现 (qt-src/databaseapi.cpp)

#### 1.1 新增方法签名
```cpp
// 同步版本
QJsonObject DatabaseApi::moveDocument(int documentId, int targetFolderId);

// 异步回调版本
void DatabaseApi::moveDocument(const QVariant &documentId, const QVariant &targetFolderId, QJSValue callback);
```

#### 1.2 核心逻辑
- **文档存在性验证**: 检查源文档是否存在
- **目标文件夹验证**: 检查目标文件夹是否存在（非根目录时）
- **重复移动检测**: 如果文档已在目标文件夹中，直接返回成功
- **关系更新**: 删除旧的 `folder_document_rel` 关系
- **排序优化**: 将目标文件夹中现有文档的 `sort_order` 都 +1
- **新关系创建**: 插入新关系，`sort_order` 设为 0（排在第一位）

#### 1.3 数据库操作
```sql
-- 1. 将目标文件夹中现有文档排序后移
UPDATE folder_document_rel SET sort_order = sort_order + 1 WHERE folder_id = ?

-- 2. 插入新关系，排在第一位
INSERT INTO folder_document_rel (folder_id, document_id, sort_order) VALUES (?, ?, 0)
```

### 2. 前端类型定义 (src/env.d.ts)

```typescript
export interface DatabaseApi {
  moveDocument(
    documentId: number | null,
    targetFolderId: number | null,
    callback: (result: DatabaseResult<void>) => void,
  ): void;
}
```

### 3. 前端封装 (src/composeables/useSqlite.ts)

```typescript
const moveDocument = async (documentId: number, targetFolderId: number | null): Promise<void> => {
  // 参数验证和安全处理
  // Promise封装Qt回调
  // 错误处理和日志记录
}
```

### 4. AI工具函数 (src/llm/tools/file.ts)

#### 4.1 函数实现
```typescript
export async function moveDocument(params: {
  documentId: number;
  targetFolderId: number | null;
}): Promise<OperationResult>
```

#### 4.2 功能特性
- **双重验证**: 验证文档和目标文件夹存在性
- **智能检测**: 检测是否为重复移动操作
- **前端同步**: 自动更新前端文件夹树状态
- **自动展开**: 移动后自动展开目标文件夹路径
- **用户反馈**: 提供详细的操作结果消息

#### 4.3 工具Schema定义
```typescript
{
  type: 'function' as const,
  function: {
    name: 'move_document',
    description: '移动文档到指定文件夹，新移入的文档将排在目标文件夹的第一位',
    parameters: {
      type: 'object',
      properties: {
        documentId: {
          type: 'number',
          description: '要移动的文档ID',
        },
        targetFolderId: {
          type: ['number', 'null'],
          description: '目标文件夹ID，null或-1表示移动到根目录',
        },
      },
      required: ['documentId', 'targetFolderId'],
    },
  },
}
```

## 关键特性

### 1. 排序优化
- **新文档优先**: 移入的文档自动排在目标文件夹第一位
- **现有文档后移**: 目标文件夹中原有文档排序自动后移
- **数据一致性**: 前后端排序状态保持同步

### 2. 错误处理
- **文档不存在**: 返回明确错误信息
- **目标文件夹不存在**: 验证目标文件夹有效性
- **重复移动**: 智能检测并友好提示
- **数据库事务**: 确保操作原子性

### 3. 前端同步
- **树状态更新**: 自动更新文件夹树显示
- **路径展开**: 移动后自动展开目标路径
- **视觉反馈**: 确保用户能看到移动结果

## 使用示例

### AI助手调用
```
用户: 把文档123移动到文件夹456中
AI: 调用 move_document({documentId: 123, targetFolderId: 456})
```

### 编程调用
```typescript
// 前端调用
const { useSqlite } = await import('src/composeables/useSqlite');
await useSqlite().moveDocument(123, 456);

// AI工具调用
const result = await moveDocument({
  documentId: 123,
  targetFolderId: 456
});
```

## 测试建议

1. **基础移动**: 测试文档在不同文件夹间的移动
2. **根目录移动**: 测试移动到根目录（targetFolderId: null）
3. **重复移动**: 测试移动到当前所在文件夹
4. **错误情况**: 测试不存在的文档ID和文件夹ID
5. **排序验证**: 确认移动后的文档排在第一位
6. **前端同步**: 验证UI状态正确更新

## 技术要点

1. **数据库设计**: 利用 `folder_document_rel` 表的 `sort_order` 字段实现排序
2. **事务处理**: 使用数据库事务确保操作原子性
3. **类型安全**: 完整的TypeScript类型定义，避免any类型
4. **Promise处理**: 正确处理Qt WebChannel的异步返回值
5. **用户体验**: 自动展开文件夹路径，确保移动结果可见

## 后续优化建议

1. **批量移动**: 支持同时移动多个文档
2. **拖拽集成**: 与现有拖拽功能集成
3. **撤销功能**: 支持移动操作的撤销
4. **移动历史**: 记录文档移动历史
5. **权限控制**: 添加文件夹访问权限验证
