# Qt WebEngine 渲染配置文件 - Windows平台专用
# 支持分离配置：Qt应用使用GPU加速，WebEngine使用软件渲染
# 这样可以获得最佳的UI性能同时避免WebEngine渲染问题

[QT_APPLICATION]
# Qt应用本身的GPU加速设置（不影响WebEngine）
# 控制Qt窗口、控件等的硬件渲染
enable_gpu_acceleration=true

# 是否使用OpenGL ES而不是Desktop OpenGL (true/false)
# 某些GPU驱动可能更兼容OpenGL ES
use_opengl_es=false

# 是否启用Qt的硬件加速合成 (true/false)
enable_hardware_composition=true

[WEBENGINE_RENDERING]
# WebEngine专用渲染设置
# 控制网页内容的渲染方式，独立于Qt应用的GPU设置

# 是否强制WebEngine使用软件渲染 (true/false)
# 启用此选项可避免WebEngine GPU相关问题，同时保持Qt应用GPU加速
force_software_rendering=true

# 是否启用2D Canvas硬件加速 (true/false)
# 仅在force_software_rendering=false时有效
enable_2d_canvas_acceleration=false

# 是否启用WebGL (true/false)
# 仅在force_software_rendering=false时有效
enable_webgl=false

# 是否启用滚动动画 (true/false)
enable_scroll_animation=false

[CHROMIUM_FLAGS]
# Windows平台专用Chromium标志 - Qt 6.9.1测试配置
# 启用之前禁用的特性以测试Qt 6.9.1的改进
# 仅保留必要的安全相关禁用选项
custom_flags=--disable-features=TranslateUI --enable-gpu-rasterization --enable-oop-rasterization --enable-zero-copy --enable-gpu-memory-buffer-video-frames

[DISPLAY]
# 缩放因子 (0.5-3.0)
# 设置为1.0以避免缩放相关的渲染问题
zoom_factor=1.0

# 是否启用高DPI支持 (true/false)
enable_high_dpi=true

[PERFORMANCE]
# 内存缓存大小 (MB)
memory_cache_size=150

# 磁盘缓存大小 (MB)
disk_cache_size=300

# 是否启用JavaScript (true/false)
enable_javascript=true

# 是否自动加载图片 (true/false)
auto_load_images=true

[WINDOWS_NATIVE]
# Windows平台原生窗口效果设置 - Qt 6.9.1测试配置
# 启用这些效果以测试Qt 6.9.1是否改善了兼容性

# 是否启用窗口阴影 (true/false)
# Qt 6.9.1测试：启用以测试渲染冲突是否已解决
enable_window_shadow=true

# 是否启用圆角窗口 (true/false) - 仅Windows 11
# Qt 6.9.1测试：启用以测试WebEngine兼容性改进
enable_rounded_corners=true

# 是否启用DWM合成 (true/false)
# Qt 6.9.1测试：启用以测试渲染冲突是否已解决
enable_dwm_composition=true

[NETWORK_ACCESS]
# 网络访问配置选项
# 用于解决生产环境中访问本地服务（如Ollama）的问题

# 是否禁用Web安全策略 (true/false)
# 启用此选项可以解决CORS问题，但会降低安全性
disable_web_security=true

# 是否允许运行不安全内容 (true/false)
# 允许HTTPS页面加载HTTP资源
allow_running_insecure_content=true

# 是否启用本地内容访问远程URL (true/false)
# 允许qrc://协议的页面访问http://localhost等本地服务
local_content_can_access_remote_urls=true

# 是否启用本地内容访问文件URL (true/false)
# 允许访问本地文件系统
local_content_can_access_file_urls=true

[TROUBLESHOOTING]
# 故障排除选项 - 向后兼容配置
# 注意：建议使用新的[WEBENGINE_RENDERING]部分进行配置

# 强制软件渲染 (true/false) - 已弃用，请使用WEBENGINE_RENDERING/force_software_rendering
# 此选项仅用于向后兼容，新配置会覆盖此设置
force_software_rendering=false

# 禁用GPU线程 (true/false) - 仅影响WebEngine
# 当WEBENGINE_RENDERING/force_software_rendering=true时会自动启用
disable_gpu_thread=false

# 启用详细日志 (true/false)
# 启用以观察Qt 6.9.1的渲染行为
enable_verbose_logging=true

# 禁用Windows原生效果 (true/false)
# Qt 6.9.1测试：允许Windows原生效果以测试兼容性改进
disable_native_effects=false

# 注意事项:
# 1. 修改此文件后需要重启应用程序
# 2. 新的配置结构支持分离渲染：
#    - [QT_APPLICATION]: 控制Qt应用本身的GPU加速（窗口、控件等）
#    - [WEBENGINE_RENDERING]: 控制WebEngine的渲染方式（网页内容）
# 3. 推荐配置：
#    - QT_APPLICATION/enable_gpu_acceleration=true（获得更好的UI性能）
#    - WEBENGINE_RENDERING/force_software_rendering=true（避免WebEngine问题）
# 4. 如果遇到Qt应用界面问题，设置QT_APPLICATION/enable_gpu_acceleration=false
# 5. 如果遇到网页渲染问题，设置WEBENGINE_RENDERING/force_software_rendering=true
