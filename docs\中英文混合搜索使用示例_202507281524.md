# 中英文混合搜索功能使用示例

**更新日期**: 2025-07-28 15:24

## 功能演示

### 1. 基本中英文混合搜索

```cpp
// 搜索包含"大模型"、"NLP"和"概念"的文件夹
QString result = databaseApi->searchFolders("大模型NLP概念");

// 自动拆分过程：
// 输入: "大模型NLP概念"
// 拆分: ["大模型", "NLP", "概念"]
// SQL: WHERE f.name LIKE '%大模型%' AND f.name LIKE '%NLP%' AND f.name LIKE '%概念%'
```

### 2. 复杂混合场景

```cpp
// AI技术相关搜索
QString result = databaseApi->searchFolders("AI人工智能研究");
// 拆分: ["AI", "人工智能研究"]

// 机器学习算法搜索
QString result = databaseApi->searchFolders("机器学习ML算法");
// 拆分: ["机器学习", "ML", "算法"]

// 深度学习框架搜索
QString result = databaseApi->searchFolders("深度学习DeepLearning框架");
// 拆分: ["深度学习", "DeepLearning", "框架"]
```

### 3. 包含数字和版本号

```cpp
// Python编程相关
QString result = databaseApi->searchFolders("Python3编程语言");
// 拆分: ["Python3", "编程语言"]

// TensorFlow版本相关
QString result = databaseApi->searchFolders("TensorFlow2深度学习");
// 拆分: ["TensorFlow2", "深度学习"]

// 模型版本搜索
QString result = databaseApi->searchFolders("GPT4大语言模型");
// 拆分: ["GPT4", "大语言模型"]
```

### 4. 多个混合关键词

```cpp
// 使用逗号分隔多个混合关键词
QString result = databaseApi->searchFolders("自然语言处理NLP,计算机视觉CV");
// 拆分: ["自然语言处理", "NLP", "计算机视觉", "CV"]

// 使用分号分隔
QString result = databaseApi->searchFolders("机器学习ML;深度学习DL");
// 拆分: ["机器学习", "ML", "深度学习", "DL"]

// 混合分隔符
QString result = databaseApi->searchFolders("数据科学DataScience, 人工智能AI; 算法Algorithm");
// 拆分: ["数据科学", "DataScience", "人工智能", "AI", "算法", "Algorithm"]
```

## 实际应用场景

### 场景1：技术文档管理

假设你有以下文件夹：
- "大模型NLP技术研究"
- "AI人工智能应用开发"
- "机器学习ML算法实现"
- "深度学习DeepLearning框架"
- "自然语言处理NLP工具"

```cpp
// 搜索NLP相关内容
QString result = databaseApi->searchFolders("NLP");
// 会找到: "大模型NLP技术研究", "自然语言处理NLP工具"

// 搜索深度学习相关
QString result = databaseApi->searchFolders("深度学习");
// 会找到: "深度学习DeepLearning框架"

// 搜索AI相关
QString result = databaseApi->searchFolders("AI");
// 会找到: "AI人工智能应用开发"
```

### 场景2：项目管理

假设你有以下项目文件夹：
- "ChatGPT聊天机器人项目"
- "BERT模型训练实验"
- "PyTorch深度学习开发"
- "TensorFlow模型部署"
- "OpenAI接口集成"

```cpp
// 搜索GPT相关项目
QString result = databaseApi->searchFolders("GPT");
// 会找到: "ChatGPT聊天机器人项目"

// 搜索PyTorch相关
QString result = databaseApi->searchFolders("PyTorch深度");
// 拆分: ["PyTorch", "深度"]
// 会找到: "PyTorch深度学习开发"

// 搜索模型相关
QString result = databaseApi->searchFolders("模型");
// 会找到: "BERT模型训练实验", "TensorFlow模型部署"
```

### 场景3：学习资料整理

假设你有以下学习资料文件夹：
- "Python3基础教程"
- "JavaScript前端开发"
- "React组件化开发"
- "Vue3响应式编程"
- "Node.js后端服务"

```cpp
// 搜索Python相关
QString result = databaseApi->searchFolders("Python3");
// 会找到: "Python3基础教程"

// 搜索前端开发相关
QString result = databaseApi->searchFolders("前端开发");
// 会找到: "JavaScript前端开发"

// 搜索Vue相关
QString result = databaseApi->searchFolders("Vue3响应式");
// 拆分: ["Vue3", "响应式"]
// 会找到: "Vue3响应式编程"
```

## 拆分规则说明

### 中文字符识别
- **Unicode范围**: `\u4e00-\u9fff` (CJK统一汉字)
- **连续匹配**: 连续的中文字符被视为一个词
- **示例**: "人工智能" → 保持为一个词

### 英文数字识别
- **字符范围**: `a-zA-Z0-9`
- **连续匹配**: 连续的英文字母和数字被视为一个词
- **示例**: "GPT4", "TensorFlow2", "Python3" → 各自保持为一个词

### 边界识别
- **中英边界**: 中文字符和英文字符之间自动分割
- **示例**: "大模型GPT" → ["大模型", "GPT"]
- **示例**: "NLP技术" → ["NLP", "技术"]

### 特殊情况处理
- **特殊字符**: 如 `+`, `-`, `.` 等会被忽略，不参与匹配
- **示例**: "C++编程" → ["C", "编程"] (++ 被忽略)
- **示例**: "Node.js开发" → ["Node", "js", "开发"] (. 被忽略)

## 性能优化建议

1. **关键词缓存**: 对常用的拆分结果进行缓存
2. **索引优化**: 在数据库中为文件夹名称字段创建全文索引
3. **限制关键词数量**: 建议单次搜索的关键词数量不超过10个
4. **异步处理**: 对于大量数据的搜索，考虑使用异步处理

## 注意事项

1. **AND逻辑**: 所有拆分后的关键词都必须匹配才会返回结果
2. **大小写不敏感**: 英文关键词搜索不区分大小写
3. **去重处理**: 重复的关键词会被自动去重
4. **空关键词**: 空的关键词会被自动忽略
5. **向后兼容**: 完全兼容原有的单关键词搜索功能
