# 移动文件夹工具实现报告

**实现时间**: 2025-07-28 19:00  
**功能**: 添加移动文件夹工具，支持将文件夹移动到根目录或其他文件夹

## 实现概述

参考移动文档工具的实现，完整添加了移动文件夹功能，包括前端和后端的完整实现链。

## 主要修改内容

### 1. 后端实现 (Qt C++)

#### 1.1 头文件声明 (`qt-src/databaseapi.h`)

- 添加 `moveFolder(const QVariant &folderId, const QVariant &targetFolderId, QJSValue callback)` 异步方法
- 添加 `moveFolder(int folderId, int targetFolderId)` 同步方法

#### 1.2 后端实现 (`qt-src/databaseapi.cpp`)

- 实现完整的 `moveFolder` 方法，包括：
  - 源文件夹存在性检查
  - 目标文件夹存在性检查（根目录除外）
  - 重复移动检查
  - 循环引用检查（防止移动到自己的子文件夹）
  - 更新 `parent_id` 字段
  - **更新 `folder_folder_rel` 关联表**（重要修正）
  - 支持移动到根目录（parent_id 设为 NULL）

### 2. 前端核心方法 (`src/composeables/useSqlite.ts`)

#### 2.1 类型定义 (`src/env.d.ts`)

- 添加 `moveFolder` 方法的 TypeScript 类型定义

#### 2.2 实现 `moveFolder` 方法

- 参数验证和错误处理
- 调用后端 API
- Promise 封装

### 3. AI 工具函数 (`src/llm/tools/file.ts`)

#### 3.1 添加 `moveFolder` 函数

- 参数验证（源文件夹和目标文件夹存在性）
- 重复移动检查
- 执行移动操作
- 前端数据同步（重新加载文件夹树）
- 友好的成功/错误消息

#### 3.2 工具配置

- 添加到 `fileTools` 导出对象
- 添加到 `tools` schema 数组
- 添加到 `fileToolMappings` 映射表

### 4. 工具配置 (`src/stores/llm.ts`)

- 在 `managementTools` 数组中添加 `move_folder`
- 确保在 agent/copilot 模式下可用

### 5. 提示词更新 (`src/services/promptService.ts`)

- 在文件夹管理工具部分添加 `move_folder` 工具说明

## 关键特性

### 1. 支持根目录移动

- 文件夹可以移动到根目录（与文档不同）
- 根目录用 `parent_id = NULL` 或 `targetFolderId = -1` 表示

### 2. 循环引用保护

- 检查目标文件夹是否是源文件夹的后代
- 防止创建无限循环的文件夹结构

### 3. 完整的错误处理

- 源文件夹不存在
- 目标文件夹不存在
- 循环引用
- 移动到自身
- 数据库操作失败

### 4. 前端数据同步

- 移动后重新加载文件夹树
- 确保 UI 状态与数据库一致

## 工具使用示例

```javascript
// AI 可以调用此工具移动文件夹
{
  "name": "move_folder",
  "parameters": {
    "folderId": 123,
    "targetFolderId": 456  // 或 -1 表示根目录
  }
}
```

## 测试建议

1. **基本移动测试**
   - 移动文件夹到其他文件夹
   - 移动文件夹到根目录

2. **边界情况测试**
   - 移动到自身（应该失败）
   - 移动到子文件夹（应该失败）
   - 移动不存在的文件夹（应该失败）
   - 移动到不存在的目标文件夹（应该失败）

3. **UI 同步测试**
   - 移动后检查文件夹树是否正确更新
   - 检查文件夹层级关系是否正确

## 注意事项

1. **与移动文档的区别**
   - 文件夹可以移动到根目录，文档不可以
   - 文件夹移动是更新 `parent_id` 字段
   - 文档移动是操作 `folder_document_rel` 关系表

2. **数据库一致性**
   - 使用事务确保操作的原子性
   - 失败时自动回滚

3. **性能考虑**
   - 移动后重新加载整个文件夹树
   - 对于大型文件夹结构可能需要优化

## 重要修正 (2025-07-28 19:30)

### 问题描述

初始实现中遗漏了对 `folder_folder_rel` 关联表的更新，该表用于管理文件夹的层级关系和排序。

### 修正内容

在 `moveFolder` 方法中添加了以下逻辑：

1. **删除旧关联关系**：

   ```sql
   DELETE FROM folder_folder_rel WHERE child_id = ?
   ```

2. **创建新关联关系**：
   - 获取目标父文件夹下的最大排序值
   - 插入新的关联记录，排序值为 `maxSortOrder + 1`

   ```sql
   INSERT INTO folder_folder_rel (parent_id, child_id, sort_order) VALUES (?, ?, ?)
   ```

3. **根目录处理**：
   - 根目录使用 `parent_id = -1` 在关联表中

### 影响

- 确保文件夹移动后的层级关系正确
- 保持文件夹排序的一致性
- 与现有的文件夹创建和更新逻辑保持一致

## 完成状态

✅ 后端 API 实现
✅ 前端核心方法
✅ AI 工具函数
✅ 工具配置
✅ 提示词更新
✅ 代码编译测试
✅ **关联表更新修正**

## 排序逻辑修正 (2025-07-28 19:35)

### 问题描述

初始实现中，移动的文件夹被放在目标文件夹的最后位置，但应该放在第一位。

### 修正内容

更新了 `folder_folder_rel` 表的排序逻辑：

1. **为新文件夹腾出第一位**：

   ```sql
   UPDATE folder_folder_rel SET sort_order = sort_order + 1 WHERE parent_id = ?
   ```

2. **插入到第一位**：
   ```sql
   INSERT INTO folder_folder_rel (parent_id, child_id, sort_order) VALUES (?, ?, 0)
   ```

### 效果

- 移动后的文件夹显示在目标文件夹内的第一个位置
- 与移动文档的排序逻辑保持一致
- 提供更好的用户体验

## 国际化支持完成 (2025-07-28 19:45)

### 实现内容

完成了 `moveFolder` 方法的国际化支持，参考 `moveDocument` 的实现模式：

1. **前端工具函数国际化**：
   - 使用 `$t()` 函数替换所有硬编码的中文消息
   - 添加参数化支持，如 `{folderId}`, `{targetFolderId}`, `{folderName}` 等

2. **翻译键定义**：
   - **中文** (`src/i18n/zh-CN/index.ts`)：

     ```typescript
     moveFolder: {
       SourceFolderNotFound: '找不到ID为 {folderId} 的源文件夹',
       TargetFolderNotFound: '找不到ID为 {targetFolderId} 的目标文件夹',
       FolderAlreadyInTarget: '文件夹已经位于目标位置中',
       success: '文件夹 "{folderName}" 已成功移动到 {targetLocation}',
       Error: '移动文件夹失败: {error}',
       operation: '移动文件夹',
       rootDirectory: '根目录',
       targetFolder: '文件夹 {targetFolderId}',
     }
     ```

   - **英文** (`src/i18n/en-US/index.ts`)：
     ```typescript
     moveFolder: {
       SourceFolderNotFound: 'Source folder not found with ID {folderId}',
       TargetFolderNotFound: 'Target folder not found with ID {targetFolderId}',
       FolderAlreadyInTarget: 'Folder is already in the target location',
       success: 'Folder "{folderName}" has been successfully moved to {targetLocation}',
       Error: 'Failed to move folder: {error}',
       operation: 'Move Folder',
       rootDirectory: 'root directory',
       targetFolder: 'folder {targetFolderId}',
     }
     ```

3. **消息格式统一**：
   - 所有错误和成功消息都包含 `operation` 字段
   - 支持动态参数替换
   - 与 `moveDocument` 的消息格式保持一致

## FolderTree 刷新和展开优化 (2025-07-28 19:50)

### 问题描述

移动文件夹和文档后，需要确保 FolderTree 组件内容被正确刷新，并使用 `expandFolderPath` 方法递归展开到被移动对象的可见状态。

### 优化内容

1. **统一数据刷新策略**：
   - `moveDocument` 和 `moveFolder` 都使用 `docStore.loadFolderTree()` 重新加载文件夹树
   - 确保移动后的数据与数据库完全同步
   - 避免前端状态不一致的问题

2. **智能文件夹展开**：
   - **moveDocument**: 始终展开到目标文件夹，确保移动后的文档可见
   - **moveFolder**:
     - 移动到具体文件夹时，展开到目标文件夹
     - 移动到根目录时，不需要展开（直接在根级别显示）

3. **展开逻辑实现**：

   ```typescript
   // moveDocument - 始终展开到目标文件夹
   await expandFolderPath(targetFolderId);

   // moveFolder - 条件性展开
   if (targetFolderId !== null && targetFolderId !== -1) {
     await expandFolderPath(targetFolderId);
   }
   ```

### 技术细节

- **expandFolderPath 函数**：递归向上查找父文件夹路径，然后触发 UI 展开
- **数据同步时机**：先执行数据库操作，再刷新前端，最后展开路径
- **用户体验**：移动操作完成后，用户可以立即看到移动后的对象位置

### 效果

- ✅ 移动后的文件夹/文档立即可见
- ✅ 文件夹树状态与数据库完全同步
- ✅ 自动展开到正确的层级位置
- ✅ 提供流畅的用户体验

移动文件夹工具已完整实现，包含完整的国际化支持和优化的 UI 刷新机制，可以在 agent/copilot 模式下使用。
