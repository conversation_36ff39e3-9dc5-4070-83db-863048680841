import { defineStore, acceptHMRUpdate } from 'pinia';
import type { SplitterWindow } from 'src/types/splitterWindow';
import type { Document } from 'src/types/doc';
import type { Editor as VueEditor } from '@tiptap/vue-3';
import type { JSONContent } from '@tiptap/vue-3';
import type { Folder } from 'src/types/doc';
import { sortFolderTree, sortFoldersAndDocuments } from 'src/utils/sortUtils';

// 编辑器实例管理器接口
interface EditorInstanceManager {
  instances: Map<string, VueEditor>;
  docToInstances: Map<number, Set<string>>; // 文档ID -> 实例键集合
  winToInstances: Map<number, Set<string>>; // 窗口ID -> 实例键集合
}

// 编辑器状态接口
interface EditorState {
  instanceKey: string;
  docId: number;
  winId: number;
  catalogVisible: boolean;
  saveStatus: 'saved' | 'saving' | 'pending' | 'error';
  lastSaveTime: number;
  isDirty: boolean;
}

// 创建编辑器实例管理器
function createEditorInstanceManager(): EditorInstanceManager {
  return {
    instances: new Map(),
    docToInstances: new Map(),
    winToInstances: new Map(),
  };
}

// 拖拽状态接口
interface DragState {
  isDragging: boolean;
  draggedDocument: Document | null;
  sourceWindowId: number | null;
  sourceIndex: number | null;
}

export const useDocStore = defineStore('doc', {
  state: () => ({
    splitterWindows: [] as SplitterWindow[],
    activeSplitterWindowId: null as number | null,
    activeDocumentId: null as number | null,
    // 新的编辑器实例管理器
    instanceManager: createEditorInstanceManager(),
    // 统一的编辑器状态管理
    editorStates: new Map<string, EditorState>(),
    documentContents: new Map<number, JSONContent>(),
    tiptapEmptyContent: {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [],
        },
      ],
    } as JSONContent,
    // 重构拖拽状态管理
    dragState: {
      isDragging: false,
      draggedDocument: null,
      sourceWindowId: null,
      sourceIndex: null,
    } as DragState,
    llmDocumentsMap: new Map<number, Document>(),
    llmDocumentKey: null as number | null,
    // 添加文件树数据管理
    folderTree: [] as Folder[],
    folderTreeLoading: false,
    // 扁平化的文件夹映射，用于快速查找
    folderMap: new Map<number, Folder>(),
    // 文档到文件夹的映射
    documentFolderMap: new Map<number, number>(),
  }),

  getters: {
    getEditorContent: (state) => (key: number) => {
      return state.documentContents.get(key);
    },
    getLlmDocumentByKey: (state) => (key: number) => {
      return state.llmDocumentsMap.get(key);
    },
    // 获取拖拽状态
    isDragging: (state) => state.dragState.isDragging,
    draggedDocument: (state) => state.dragState.draggedDocument,
    // 获取文件夹下的所有文档（递归）
    getDocumentsInFolder:
      (state) =>
      (folderId: number): Document[] => {
        const folder = state.folderMap.get(folderId);
        if (!folder) return [];

        let documents = [...(folder.documents || [])];

        // 递归获取子文件夹中的文档
        if (folder.children) {
          for (const childFolder of folder.children) {
            const getChildDocs = (id: number): Document[] => {
              const childFolderData = state.folderMap.get(id);
              if (!childFolderData) return [];

              let childDocs = [...(childFolderData.documents || [])];

              if (childFolderData.children) {
                for (const grandChild of childFolderData.children) {
                  childDocs = childDocs.concat(getChildDocs(grandChild.id));
                }
              }

              return childDocs;
            };

            documents = documents.concat(getChildDocs(childFolder.id));
          }
        }

        return documents;
      },
    // 根据文档ID获取所属文件夹ID
    getDocumentFolderId:
      (state) =>
      (docId: number): number => {
        return state.documentFolderMap.get(docId) || -1; // -1表示不在任何文件夹中
      },
  },

  actions: {
    setLlmDocumentKey(key: number) {
      this.llmDocumentKey = key;
    },
    setLlmDocumentsMap(doc: Document) {
      this.llmDocumentsMap.set(doc.id, doc);
    },

    // 新的实例键生成策略 - 使用字符串避免冲突
    generateEditorInstanceKey(winId: number, docId: number): string {
      return `editor_${winId}_${docId}`;
    },

    // === 新的编辑器实例管理方法 ===

    /**
     * 注册编辑器实例
     */
    registerEditorInstance(winId: number, docId: number, editor: VueEditor): string {
      const instanceKey = this.generateEditorInstanceKey(winId, docId);

      // 添加到实例映射
      this.instanceManager.instances.set(instanceKey, editor);

      // 更新文档到实例的映射
      if (!this.instanceManager.docToInstances.has(docId)) {
        this.instanceManager.docToInstances.set(docId, new Set());
      }
      this.instanceManager.docToInstances.get(docId)!.add(instanceKey);

      // 更新窗口到实例的映射
      if (!this.instanceManager.winToInstances.has(winId)) {
        this.instanceManager.winToInstances.set(winId, new Set());
      }
      this.instanceManager.winToInstances.get(winId)!.add(instanceKey);

      // 初始化编辑器状态
      this.editorStates.set(instanceKey, {
        instanceKey,
        docId,
        winId,
        catalogVisible: false,
        saveStatus: 'saved',
        lastSaveTime: Date.now(),
        isDirty: false,
      });

      return instanceKey;
    },

    /**
     * 获取编辑器实例
     */
    getEditorInstance(winId: number, docId: number): VueEditor | null {
      const instanceKey = this.generateEditorInstanceKey(winId, docId);
      return this.instanceManager.instances.get(instanceKey) || null;
    },

    /**
     * 获取编辑器实例（通过实例键）
     */
    getEditorInstanceByKey(instanceKey: string): VueEditor | null {
      return this.instanceManager.instances.get(instanceKey) || null;
    },

    /**
     * 根据编辑器实例查找对应的实例键
     */
    findInstanceKeyByEditor(editor: VueEditor): string | null {
      for (const [instanceKey, instance] of this.instanceManager.instances.entries()) {
        if (instance === editor) {
          return instanceKey;
        }
      }
      return null;
    },

    /**
     * 移除编辑器实例
     */
    unregisterEditorInstance(winId: number, docId: number): void {
      const instanceKey = this.generateEditorInstanceKey(winId, docId);

      // 从实例映射中移除
      const editor = this.instanceManager.instances.get(instanceKey);
      if (editor) {
        editor.destroy();
        this.instanceManager.instances.delete(instanceKey);
      }

      // 从文档到实例的映射中移除
      const docInstances = this.instanceManager.docToInstances.get(docId);
      if (docInstances) {
        docInstances.delete(instanceKey);
        if (docInstances.size === 0) {
          this.instanceManager.docToInstances.delete(docId);
        }
      }

      // 从窗口到实例的映射中移除
      const winInstances = this.instanceManager.winToInstances.get(winId);
      if (winInstances) {
        winInstances.delete(instanceKey);
        if (winInstances.size === 0) {
          this.instanceManager.winToInstances.delete(winId);
        }
      }

      // 清理编辑器状态
      this.editorStates.delete(instanceKey);
    },

    /**
     * 获取文档的所有编辑器实例键
     */
    getDocumentInstanceKeys(docId: number): string[] {
      const instanceKeys = this.instanceManager.docToInstances.get(docId);
      return instanceKeys ? Array.from(instanceKeys) : [];
    },

    /**
     * 获取窗口的所有编辑器实例键
     */
    getWindowInstanceKeys(winId: number): string[] {
      const instanceKeys = this.instanceManager.winToInstances.get(winId);
      return instanceKeys ? Array.from(instanceKeys) : [];
    },

    // 没有直接关闭win的事件，此事件暂时无用
    removeEditorInstancesByWinId(winId: number) {
      // 使用新的实例管理器
      const instanceKeys = this.getWindowInstanceKeys(winId);
      instanceKeys.forEach((instanceKey: string) => {
        const editor = this.instanceManager.instances.get(instanceKey);
        if (editor) {
          const docId = this.editorStates.get(instanceKey)?.docId;
          if (docId !== undefined) {
            this.unregisterEditorInstance(winId, docId);
          }
        }
      });
    },

    destroyEditorInstances() {
      // 使用新的实例管理器销毁所有实例
      this.instanceManager.instances.forEach((editor: VueEditor) => {
        editor.destroy();
      });
      this.instanceManager.instances.clear();
      this.instanceManager.docToInstances.clear();
      this.instanceManager.winToInstances.clear();
      this.editorStates.clear();
      this.documentContents.clear();
    },

    setActiveDocumentId(winId: number, docId: number) {
      this.splitterWindows.find((w: SplitterWindow) => w.id === winId).activeDocumentId = docId;
    },
    getActiveDocumentId(winId: number) {
      return this.splitterWindows.find((w: SplitterWindow) => w.id === winId).activeDocumentId;
    },
    getActiveWindow() {
      return this.splitterWindows.find((w: SplitterWindow) => w.id === this.activeSplitterWindowId);
    },
    getActiveDocumentByWinId(winId: number) {
      const win = this.splitterWindows.find((w: SplitterWindow) => w.id === winId);
      if (!win) {
        return null;
      }
      const activeDocOfWin = win.documents.find((d: Document) => d.id === win.activeDocumentId);
      if (!activeDocOfWin) {
        return null;
      }
      return activeDocOfWin;
    },
    setActiveSplitterWindowId(windowId: number) {
      // console.log('setActiveSplitterWindowId', windowId);
      this.activeSplitterWindowId = windowId;
    },
    // 获取当前聚焦的文档id，用于AI聚焦目标
    getFocusedDocument() {
      const focusedWindow = this.getActiveWindow();
      if (!focusedWindow) {
        return null;
      }
      const focusedDocument = this.getActiveDocumentByWinId(focusedWindow.id);
      if (!focusedDocument) {
        return null;
      }
      return focusedDocument;
    },
    removeDocument(winId: number, docId: number) {
      const win = this.splitterWindows.find((w: SplitterWindow) => w.id === winId);
      if (!win) {
        return;
      }
      if (win.documents?.length > 0) {
        const indexOfDoc = win.documents.findIndex((d: Document) => d.id === docId);
        if (indexOfDoc !== -1) {
          // 设置相邻文档为活动文档，tab组件监听activeDocumentId变化，自动切换tab
          win.activeDocumentId =
            win.documents[indexOfDoc - 1]?.id || win.documents[indexOfDoc + 1]?.id || 0;
        }
        this.unregisterEditorInstance(winId, docId);
        win.documents.splice(indexOfDoc, 1);
      }
      this.splitterWindows = this.splitterWindows.filter(
        (w: SplitterWindow) => w.documents.length > 0,
      );
    },
    /**
     * 移除指定窗口内的重复文档
     * @param winId 窗口ID
     * @returns 移除的文档数量
     */
    removeDuplicateDocuments(winId: number): number {
      const win = this.splitterWindows.find((w: SplitterWindow) => w.id === winId);
      if (!win) {
        return 0;
      }

      const uniqueDocs = new Map<number, Document>();
      let removedCount = 0;

      // 保留最后一个出现的文档
      win.documents.forEach((doc: Document) => {
        if (uniqueDocs.has(doc.id)) {
          removedCount++;
        } else {
          uniqueDocs.set(doc.id, doc);
        }
      });

      // 更新窗口的文档列表
      win.documents = Array.from(uniqueDocs.values());

      // 如果活动文档被移除，设置新的活动文档
      if (!win.documents.find((d: Document) => d.id === win.activeDocumentId)) {
        win.activeDocumentId = win.documents[0]?.id || 0;
      }

      return removedCount;
    },
    splitDocument(leftWinIndex: number, doc: Document, folderId: number) {
      // console.log('splitDocument', leftWinIndex, doc);

      const newWin = {
        id: this.splitterWindows.length + 1,
        flex: '1',
        documents: [doc],
        folderId,
      };
      this.splitterWindows.splice(leftWinIndex + 1, 0, newWin);
      this.setActiveSplitterWindowId(newWin.id);
      this.setActiveDocumentId(newWin.id, doc.id);
    },

    // ========== 重构的拖拽方法 ==========

    /**
     * 开始拖拽文档
     * @param windowId 源窗口ID
     * @param docId 文档ID
     */
    startDragDocument(windowId: number, docId: number) {
      const window = this.splitterWindows.find((w) => w.id === windowId);
      if (!window) return;

      const docIndex = window.documents.findIndex((d) => d.id === docId);
      if (docIndex === -1) return;

      const document = window.documents[docIndex];

      this.dragState = {
        isDragging: true,
        draggedDocument: document,
        sourceWindowId: windowId,
        sourceIndex: docIndex,
      };
    },

    /**
     * 结束拖拽
     */
    endDragDocument() {
      this.dragState = {
        isDragging: false,
        draggedDocument: null,
        sourceWindowId: null,
        sourceIndex: null,
      };
    },

    /**
     * 检查文档是否已存在于指定窗口中
     * @param windowId 窗口ID
     * @param docId 文档ID
     * @returns 如果存在返回true，否则返回false
     */
    isDocumentInWindow(windowId: number, docId: number): boolean {
      const window = this.splitterWindows.find((w) => w.id === windowId);
      if (!window) return false;
      return window.documents.some((doc) => doc.id === docId);
    },

    /**
     * 获取包含指定文档的窗口
     * @param docId 文档ID
     * @returns 包含该文档的窗口，如果不存在返回null
     */
    getWindowContainingDocument(docId: number): SplitterWindow | null {
      return this.splitterWindows.find((w) => w.documents.some((doc) => doc.id === docId)) || null;
    },

    /**
     * 在同一窗口内排序文档
     * @param windowId 窗口ID
     * @param oldIndex 原位置
     * @param newIndex 新位置
     */
    sortDocumentsInWindow(windowId: number, oldIndex: number, newIndex: number) {
      const window = this.splitterWindows.find((w) => w.id === windowId);
      if (!window || oldIndex === newIndex) return;

      const [movedDoc] = window.documents.splice(oldIndex, 1);
      window.documents.splice(newIndex, 0, movedDoc);
    },

    /**
     * 将文档移动到另一个窗口
     * @param targetWindowId 目标窗口ID
     * @param targetIndex 目标位置
     */
    moveDocumentToWindow(targetWindowId: number, targetIndex: number) {
      if (!this.dragState.isDragging || !this.dragState.draggedDocument) return;

      const { sourceWindowId, sourceIndex, draggedDocument } = this.dragState;

      // 检查目标窗口是否已存在相同文档
      const targetWindow = this.splitterWindows.find((w) => w.id === targetWindowId);
      if (targetWindow && targetWindow.documents.some((doc) => doc.id === draggedDocument.id)) {
        // 如果目标窗口已存在相同文档，只从源窗口移除，不添加到目标窗口
        if (sourceWindowId !== null && sourceIndex !== null) {
          const sourceWindow = this.splitterWindows.find((w) => w.id === sourceWindowId);
          if (sourceWindow) {
            sourceWindow.documents.splice(sourceIndex, 1);

            // 如果源窗口没有文档了，移除该窗口
            if (sourceWindow.documents.length === 0) {
              this.splitterWindows = this.splitterWindows.filter((w) => w.id !== sourceWindowId);
            } else {
              // 设置新的活动文档
              sourceWindow.activeDocumentId = sourceWindow.documents[0]?.id || 0;
            }
          }
        }

        // 设置目标窗口为活动窗口，拖拽的文档为活动文档
        this.setActiveSplitterWindowId(targetWindowId);
        this.setActiveDocumentId(targetWindowId, draggedDocument.id);
        return;
      }

      // 从源窗口移除文档
      if (sourceWindowId !== null && sourceIndex !== null) {
        const sourceWindow = this.splitterWindows.find((w) => w.id === sourceWindowId);
        if (sourceWindow) {
          sourceWindow.documents.splice(sourceIndex, 1);

          // 如果源窗口没有文档了，移除该窗口
          if (sourceWindow.documents.length === 0) {
            this.splitterWindows = this.splitterWindows.filter((w) => w.id !== sourceWindowId);
          } else {
            // 设置新的活动文档
            sourceWindow.activeDocumentId = sourceWindow.documents[0]?.id || 0;
          }
        }
      }

      // 添加到目标窗口
      if (targetWindow) {
        targetWindow.documents.splice(targetIndex, 0, draggedDocument);
        targetWindow.activeDocumentId = draggedDocument.id;
        this.setActiveSplitterWindowId(targetWindowId);
      }
    },

    /**
     * 处理拖拽完成事件
     * @param targetWindowId 目标窗口ID
     * @param targetIndex 目标位置
     */
    handleDragComplete(targetWindowId: number, targetIndex: number) {
      if (!this.dragState.isDragging) return;

      const { sourceWindowId } = this.dragState;

      if (sourceWindowId === targetWindowId) {
        // 同一窗口内排序
        this.sortDocumentsInWindow(targetWindowId, this.dragState.sourceIndex, targetIndex);
        // 排序后检查并移除重复文档
        this.removeDuplicateDocuments(targetWindowId);
      } else {
        // 跨窗口移动
        this.moveDocumentToWindow(targetWindowId, targetIndex);
      }

      // 清理拖拽状态
      this.endDragDocument();
    },

    // 兼容旧方法的包装器
    setDragDocument(winId: number, docId: number) {
      this.startDragDocument(winId, docId);
    },

    dragDocmentInWin(winId: number, newIndex: number) {
      this.handleDragComplete(winId, newIndex);
    },

    sortDocuments(winId: number, newIndex: number, oldIndex: number) {
      this.sortDocumentsInWindow(winId, oldIndex, newIndex);
    },

    dragDocmentOutWin(winId: number) {
      if (this.dragState.draggedDocument) {
        this.removeDocument(winId, this.dragState.draggedDocument.id);
      }
      this.endDragDocument();
    },

    syncDocuments(doc: Document) {
      this.llmDocumentsMap.forEach((d: Document) => {
        if (d.id === doc.id) {
          this.llmDocumentsMap.set(doc.id, doc);
        }
      });
      this.llmDocumentKey = doc.id;
      this.splitterWindows.forEach((w: SplitterWindow) => {
        if (w.documents.find((d: Document) => d.id === doc.id)) {
          w.documents = w.documents.map((e: Document) => {
            if (e.id === doc.id) {
              return doc;
            }
            return e;
          });
        }
      });
    },
    // === 新的内容同步机制 ===

    /**
     * 更新编辑器内容（新版本 - 使用字符串实例键）
     */
    updateEditorContentV2(docId: number, content: JSONContent, excludeInstanceKey?: string) {
      // 存储内容到 Map 中
      this.documentContents.set(docId, content);

      // 使用新的快速同步机制
      this.syncContentToEditorInstancesV2(docId, content, excludeInstanceKey);
    },

    /**
     * 基于索引的快速内容同步 - O(1) 复杂度
     * @param docId 文档ID
     * @param content 文档内容
     * @param excludeInstanceKey 排除的实例键
     */
    syncContentToEditorInstancesV2(
      docId: number,
      content: JSONContent,
      excludeInstanceKey?: string,
    ) {
      // 直接通过索引获取相同文档的所有实例键 - O(1) 复杂度
      const instanceKeys = this.instanceManager.docToInstances.get(docId);
      if (!instanceKeys || instanceKeys.size === 0) {
        return;
      }

      // 批量同步所有相关实例
      const syncPromises: Promise<void>[] = [];

      for (const instanceKey of instanceKeys) {
        // 跳过当前触发修改的编辑器实例
        if (excludeInstanceKey && instanceKey === excludeInstanceKey) {
          continue;
        }

        const editor = this.instanceManager.instances.get(instanceKey);
        if (!editor) {
          continue;
        }

        // 异步比较和同步内容，避免阻塞主线程
        const syncPromise = this.syncSingleEditorContent(instanceKey, editor, content);
        syncPromises.push(syncPromise);
      }

      // 批量处理所有同步操作
      if (syncPromises.length > 0) {
        void Promise.allSettled(syncPromises).then((results) => {
          const failedCount = results.filter((r) => r.status === 'rejected').length;
          if (failedCount > 0) {
            console.warn(`内容同步完成，${failedCount} 个实例同步失败`);
          }
        });
      }
    },

    /**
     * 同步单个编辑器内容
     */
    async syncSingleEditorContent(
      instanceKey: string,
      editor: VueEditor,
      content: JSONContent,
    ): Promise<void> {
      try {
        const currentContent = editor.getJSON();

        // 使用更高效的内容比较
        if (!this.isContentEqual(currentContent, content)) {
          // 通过自定义事件通知编辑器组件开始同步
          this.dispatchSyncEvent(instanceKey, content, true);

          // 同步内容
          editor.commands.setContent(content, false);

          // 更新编辑器状态
          const editorState = this.editorStates.get(instanceKey);
          if (editorState) {
            editorState.isDirty = false;
            editorState.lastSaveTime = Date.now();
          }

          // 延迟通知同步结束，确保内容已更新
          await new Promise((resolve) => setTimeout(resolve, 50));
          this.dispatchSyncEvent(instanceKey, content, false);
        }
      } catch (error) {
        console.error(`同步编辑器内容失败 [${instanceKey}]:`, error);
        // 确保在出错时也通知同步结束
        this.dispatchSyncEvent(instanceKey, content, false);
        throw error;
      }
    },

    /**
     * 高效的内容比较
     */
    isContentEqual(content1: JSONContent, content2: JSONContent): boolean {
      // 首先比较基本属性
      if (content1.type !== content2.type) return false;

      // 对于简单内容，直接比较
      if (!content1.content && !content2.content) {
        return JSON.stringify(content1) === JSON.stringify(content2);
      }

      // 对于复杂内容，使用更高效的比较策略
      try {
        return JSON.stringify(content1) === JSON.stringify(content2);
      } catch {
        return false;
      }
    },

    /**
     * 派发同步事件
     */
    dispatchSyncEvent(instanceKey: string, content: JSONContent, isStart: boolean) {
      const event = new CustomEvent('editorContentSync', {
        detail: { instanceKey, content, isStart },
      });
      window.dispatchEvent(event);
    },

    clearEditorContent(docId: number) {
      this.documentContents.delete(docId);
    },

    // === 统一的编辑器状态管理 ===

    /**
     * 获取编辑器状态
     */
    getEditorState(instanceKey: string): EditorState | null {
      return this.editorStates.get(instanceKey) || null;
    },

    /**
     * 更新编辑器状态
     */
    updateEditorState(instanceKey: string, updates: Partial<EditorState>): void {
      const currentState = this.editorStates.get(instanceKey);
      if (currentState) {
        const newState = { ...currentState, ...updates };
        this.editorStates.set(instanceKey, newState);
      }
    },

    /**
     * 批量更新编辑器状态
     */
    batchUpdateEditorStates(
      updates: Array<{ instanceKey: string; updates: Partial<EditorState> }>,
    ): void {
      updates.forEach(({ instanceKey, updates: stateUpdates }) => {
        this.updateEditorState(instanceKey, stateUpdates);
      });
    },

    /**
     * 获取文档的所有编辑器状态
     */
    getDocumentEditorStates(docId: number): EditorState[] {
      const instanceKeys = this.getDocumentInstanceKeys(docId);
      return instanceKeys
        .map((key) => this.editorStates.get(key))
        .filter((state): state is EditorState => state !== undefined);
    },

    /**
     * 获取窗口的所有编辑器状态
     */
    getWindowEditorStates(winId: number): EditorState[] {
      const instanceKeys = this.getWindowInstanceKeys(winId);
      return instanceKeys
        .map((key) => this.editorStates.get(key))
        .filter((state): state is EditorState => state !== undefined);
    },

    /**
     * 设置目录可见性
     */
    setCatalogVisible(instanceKey: string, visible: boolean): void {
      this.updateEditorState(instanceKey, { catalogVisible: visible });
    },

    /**
     * 获取目录可见性
     */
    getCatalogVisible(instanceKey: string): boolean {
      const state = this.getEditorState(instanceKey);
      return state?.catalogVisible || false;
    },

    /**
     * 设置保存状态
     */
    setSaveStatus(instanceKey: string, status: 'saved' | 'saving' | 'pending' | 'error'): void {
      this.updateEditorState(instanceKey, {
        saveStatus: status,
        lastSaveTime: status === 'saved' ? Date.now() : undefined,
      });
    },

    /**
     * 获取保存状态
     */
    getSaveStatus(instanceKey: string): 'saved' | 'saving' | 'pending' | 'error' {
      const state = this.getEditorState(instanceKey);
      return state?.saveStatus || 'saved';
    },

    /**
     * 标记编辑器为脏状态
     */
    markEditorDirty(instanceKey: string, isDirty: boolean = true): void {
      this.updateEditorState(instanceKey, { isDirty });
    },

    /**
     * 检查编辑器是否为脏状态
     */
    isEditorDirty(instanceKey: string): boolean {
      const state = this.getEditorState(instanceKey);
      return state?.isDirty || false;
    },

    /**
     * 清理编辑器状态
     */
    cleanupEditorState(instanceKey: string): void {
      this.editorStates.delete(instanceKey);
    },

    /**
     * 获取所有脏状态的编辑器
     */
    getDirtyEditors(): EditorState[] {
      return Array.from(this.editorStates.values()).filter(
        (state): state is EditorState => (state as EditorState).isDirty,
      );
    },

    /**
     * 获取需要保存的编辑器（脏状态且超过一定时间未保存）
     */
    getEditorsNeedingSave(maxUnsavedTime: number = 10000): EditorState[] {
      const now = Date.now();
      return Array.from(this.editorStates.values()).filter((state): state is EditorState => {
        const editorState = state as EditorState;
        return editorState.isDirty && now - editorState.lastSaveTime > maxUnsavedTime;
      });
    },

    // ========== 文件树管理方法 ==========

    /**
     * 加载文件夹树数据
     */
    async loadFolderTree(parentId: number | null = null) {
      // 防止重复加载：如果已经在加载中，直接返回
      if (this.folderTreeLoading) {
        console.log('🔄 [DocStore] 文件夹树正在加载中，跳过重复请求, parentId:', parentId);
        return;
      }

      const { useSqlite } = await import('src/composeables/useSqlite');
      this.folderTreeLoading = true;

      try {
        console.log('🔄 [DocStore] 开始加载文件夹树, parentId:', parentId);

        // 确保数据库已初始化
        const sqliteInstance = useSqlite();
        await sqliteInstance.initialize();

        const folders = await sqliteInstance.listFolders(parentId);
        console.log('✅ [DocStore] 文件夹树加载成功, 文件夹数量:', folders.length);

        // 获取当前排序模式
        const { useUiStore } = await import('src/stores/ui');
        const uiStore = useUiStore();
        const currentSortMode = uiStore.sortMode;
        console.log('🔄 [DocStore] 当前排序模式:', currentSortMode);

        if (parentId === null) {
          // 根级别加载，替换整个树并排序
          this.folderTree = sortFolderTree(folders, currentSortMode);
          console.log('🔄 [DocStore] 根级别排序完成，文件夹数量:', this.folderTree.length);
        } else {
          // 子级别加载，更新对应节点并排序
          this.updateFolderInTree(parentId, sortFolderTree(folders, currentSortMode));
          console.log('🔄 [DocStore] 子级别排序完成，parentId:', parentId);
        }
        this.buildFolderMaps();
      } catch (error) {
        console.error('❌ [DocStore] 加载文件夹树失败:', error);

        // 如果是根级别加载失败，确保folderTree不为undefined
        if (parentId === null && !this.folderTree) {
          this.folderTree = [];
        }

        // 重新抛出错误，让调用者处理
        throw error;
      } finally {
        this.folderTreeLoading = false;
      }
    },

    /**
     * 构建文件夹和文档的扁平化映射
     */
    buildFolderMaps() {
      this.folderMap.clear();
      this.documentFolderMap.clear();

      const processFolder = (folder: Folder) => {
        // 检查folder是否有效
        if (!folder || typeof folder.id !== 'number' || folder.id <= 0) {
          console.warn('Invalid folder detected:', folder);
          return;
        }

        this.folderMap.set(folder.id, folder);

        // 建立文档到文件夹的映射
        if (folder.documents) {
          for (const doc of folder.documents) {
            // 检查document是否有效
            if (!doc || typeof doc.id !== 'number' || doc.id <= 0) {
              console.warn('Invalid document detected:', doc);
              continue;
            }
            this.documentFolderMap.set(doc.id, folder.id);
          }
        }

        // 递归处理子文件夹
        if (folder.children) {
          for (const child of folder.children) {
            processFolder(child);
          }
        }
      };

      for (const folder of this.folderTree) {
        processFolder(folder);
      }
    },

    /**
     * 在文件树中查找并更新指定文件夹
     */
    updateFolderInTree(folderId: number, children: Folder[]) {
      const updateInArray = (folders: Folder[]): boolean => {
        for (let i = 0; i < folders.length; i++) {
          if (folders[i].id === folderId) {
            folders[i].children = children;
            return true;
          }
          if (folders[i].children && updateInArray(folders[i].children)) {
            return true;
          }
        }
        return false;
      };
      updateInArray(this.folderTree);
    },

    /**
     * 添加新文档到文件树
     */
    async addDocumentToTree(document: Document, folderId: number) {
      // 获取当前排序模式
      const { useUiStore } = await import('src/stores/ui');
      const uiStore = useUiStore();
      const currentSortMode = uiStore.sortMode;

      const folder = this.folderMap.get(folderId);
      if (folder) {
        if (!folder.documents) {
          folder.documents = [];
        }
        folder.documents.push(document);
        // 重新排序文档
        const { sortedDocuments } = sortFoldersAndDocuments([], folder.documents, currentSortMode);
        folder.documents = sortedDocuments;
        this.documentFolderMap.set(document.id, folderId);

        // 强制触发响应式更新 - 重建映射确保数据一致性
        this.buildFolderMaps();
      } else {
        console.warn(`无法找到文件夹 ID: ${folderId}，尝试重新加载文件树`);
        // 如果找不到文件夹，重新加载整个文件树
        void this.loadFolderTree();
      }
    },

    /**
     * 从文件树中移除文档
     */
    removeDocumentFromTree(docId: number) {
      const folderId = this.documentFolderMap.get(docId);
      if (folderId) {
        const folder = this.folderMap.get(folderId);
        if (folder && folder.documents) {
          const index = folder.documents.findIndex((doc) => doc.id === docId);
          if (index !== -1) {
            folder.documents.splice(index, 1);
          }
        }
        this.documentFolderMap.delete(docId);
      }
    },

    /**
     * 更新文件树中的文档信息
     */
    updateDocumentInTree(document: Document) {
      const folderId = this.documentFolderMap.get(document.id);
      if (folderId) {
        const folder = this.folderMap.get(folderId);
        if (folder && folder.documents) {
          const index = folder.documents.findIndex((doc) => doc.id === document.id);
          if (index !== -1) {
            folder.documents[index] = document;
          }
        }
      }
    },

    /**
     * 添加新文件夹到文件树
     */
    async addFolderToTree(folder: Folder, parentId: number | null) {
      // 获取当前排序模式
      const { useUiStore } = await import('src/stores/ui');
      const uiStore = useUiStore();
      const currentSortMode = uiStore.sortMode;

      if (parentId === null) {
        // 根级别文件夹
        this.folderTree.push(folder);
        // 重新排序根级别文件夹
        this.folderTree = sortFolderTree(this.folderTree, currentSortMode);
      } else {
        // 子文件夹
        const parentFolder = this.folderMap.get(parentId);
        if (parentFolder) {
          if (!parentFolder.children) {
            parentFolder.children = [];
          }
          parentFolder.children.push(folder);
          // 重新排序子文件夹
          const { sortedFolders } = sortFoldersAndDocuments(
            parentFolder.children,
            [],
            currentSortMode,
          );
          parentFolder.children = sortedFolders;
        } else {
          console.warn(`无法找到父文件夹 ID: ${parentId}，尝试重新加载文件树`);
          // 如果找不到父文件夹，重新加载整个文件树
          void this.loadFolderTree();
          return;
        }
      }
      this.folderMap.set(folder.id, folder);

      // 强制触发响应式更新
      this.buildFolderMaps();
    },

    /**
     * 从文件树中移除文件夹
     */
    removeFolderFromTree(folderId: number) {
      const folder = this.folderMap.get(folderId);
      if (!folder) return;

      // 递归删除所有子文件夹和文档的映射
      const removeRecursive = (f: Folder) => {
        if (f.documents) {
          for (const doc of f.documents) {
            this.documentFolderMap.delete(doc.id);
          }
        }
        if (f.children) {
          for (const child of f.children) {
            removeRecursive(child);
            this.folderMap.delete(child.id);
          }
        }
      };
      removeRecursive(folder);

      // 从父级中移除
      if (folder.parent_id === -1) {
        const index = this.folderTree.findIndex((f) => f.id === folderId);
        if (index !== -1) {
          this.folderTree.splice(index, 1);
        }
      } else {
        const parentFolder = this.folderMap.get(folder.parent_id);
        if (parentFolder && parentFolder.children) {
          const index = parentFolder.children.findIndex((f) => f.id === folderId);
          if (index !== -1) {
            parentFolder.children.splice(index, 1);
          }
        }
      }

      this.folderMap.delete(folderId);
    },

    /**
     * 更新文件树中的文件夹信息
     */
    updateFolderInTreeData(folder: Folder) {
      const existingFolder = this.folderMap.get(folder.id);
      if (existingFolder) {
        // 保留原有的children和documents，只更新其他字段
        Object.assign(existingFolder, {
          ...folder,
          children: existingFolder.children,
          documents: existingFolder.documents,
        });
      }
    },

    /**
     * 移动文档到新的文件夹
     */
    async moveDocumentToFolder(documentId: number, targetFolderId: number | null) {
      const { useSqlite } = await import('src/composeables/useSqlite');

      // 验证不允许移动文档到根目录
      if (targetFolderId === null || targetFolderId === -1) {
        throw new Error('不允许将文档移动到根目录，请选择一个文件夹');
      }

      // 获取文档当前信息
      const document = await useSqlite().getDocument(documentId);
      if (!document) return false;

      // 更新数据库中的文档文件夹关系
      await useSqlite().updateDocument(
        documentId,
        document.title || '',
        JSON.parse(document.content || '{"type":"doc","content":[]}'),
        targetFolderId,
        document.metadata || '{}',
      );

      // 从原文件夹中移除文档
      this.removeDocumentFromTree(documentId);

      // 添加到新文件夹
      const updatedDocument = await useSqlite().getDocument(documentId);
      this.addDocumentToTree(updatedDocument, targetFolderId);

      return true;
    },

    /**
     * 移动文件夹到新的父文件夹
     */
    async moveFolderToParent(folderId: number, targetParentId: number | null) {
      const { useSqlite } = await import('src/composeables/useSqlite');

      // 获取文件夹当前信息
      const folder = this.folderMap.get(folderId);
      if (!folder) return false;

      // 检查是否会造成循环引用
      // 1. 不能移动到自己
      if (targetParentId === folderId) {
        console.warn('Cannot move folder to itself');
        return false;
      }

      // 2. 不能移动到自己的子文件夹（后代文件夹）
      if (targetParentId !== null && this.isDescendantOf(folderId, targetParentId)) {
        console.warn('Cannot move folder to its descendant');
        return false;
      }

      // 保存原有的完整文件夹数据（包括子文件夹和文档）
      // 使用深拷贝确保子文件夹和文档数据不会丢失
      const originalFolderData: Folder = {
        ...folder,
        children: folder.children ? [...folder.children] : undefined,
        documents: folder.documents ? [...folder.documents] : undefined,
      };

      // 更新数据库中的文件夹父级关系
      await useSqlite().updateFolder(folderId, folder.name || '', targetParentId);

      // 从原父级中移除文件夹，但不删除映射关系（避免子文件夹和文档数据丢失）
      if (folder.parent_id === -1) {
        // 从根级别移除
        const index = this.folderTree.findIndex((f) => f.id === folderId);
        if (index !== -1) {
          this.folderTree.splice(index, 1);
        }
      } else {
        // 从父文件夹的children中移除
        const parentFolder = this.folderMap.get(folder.parent_id);
        if (parentFolder && parentFolder.children) {
          const index = parentFolder.children.findIndex((f) => f.id === folderId);
          if (index !== -1) {
            parentFolder.children.splice(index, 1);
          }
        }
      }

      // 不重新获取数据，直接使用原有数据并更新 parent_id
      // 这样可以保持所有子文件夹和文档的完整数据
      const updatedFolder: Folder = {
        ...originalFolderData,
        parent_id: targetParentId === null ? -1 : targetParentId, // 更新父级ID
        updated_at: new Date().toISOString(), // 更新时间戳
      };

      // 添加到新父级
      this.addFolderToTree(updatedFolder, targetParentId);

      return true;
    },

    /**
     * 检查文件夹是否是另一个文件夹的后代
     */
    isDescendantOf(ancestorId: number, descendantId: number): boolean {
      const checkDescendant = (folderId: number): boolean => {
        const folder = this.folderMap.get(folderId);
        if (!folder || !folder.children) return false;

        for (const child of folder.children) {
          if (child.id === descendantId) return true;
          if (this.isDescendantOf(child.id, descendantId)) return true;
        }
        return false;
      };

      return checkDescendant(ancestorId);
    },
  },
});
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useDocStore, import.meta.hot));
}
