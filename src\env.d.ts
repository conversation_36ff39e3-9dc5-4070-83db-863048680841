import type { Store } from 'pinia';
import type { Folder, Document } from './types/doc';
import type {
  Conversation,
  QwenSettings,
  OllamaSettings,
  MiniMaxSettings,
  DeepSeekSettings,
  VolcesSettings,
  MoonshotSettings,
  AnthropicSettings,
  KnowledgeBaseSettings,
  GrokSettings,
} from './types/qwen';
import type { OpenAISettings } from './types/openai';
import type { AzureOpenAISettings } from './types/azureOpenai';
import type { GeminiSettings } from './types/gemini';
import type { GlmSettings } from './types/glm';

// --- API接口定义 ---

export type ChatMode = 'agent' | 'copilot' | 'chat';

/**
 * 对应 C++ 端 windowapi.h 的完整接口
 */
export interface WindowApi {
  minimizeWindow(): void;
  toggleMaximizeWindow(): void;
  closeWindow(): void;
  reloadPage(): void;
  openDevTools(): void;
  toggleMode(): void;
  toggleTheme(): void;
  toggleLeftDrawer(): void;
  toggleRightDrawer(): void;
  startWindowDrag(x: number, y: number): void;
  moveWindow(x: number, y: number): void;
  endWindowDrag(): void;
  hasThemeSetting(): boolean;
  getSavedTheme(): Promise<boolean>;
  saveThemeSetting(isDark: boolean): void;
  setThemeFromVue(isDark: boolean): void;
  onThemeChangedFromFrontend(): void;
  setThemeDirectly(isDark: boolean): void;
  openExternalUrl(url: string): void;
}

/**
 * @description C++ 暴露的 databaseApi 对象的 TypeScript 定义
 */
export interface DatabaseApi {
  // 基础CRUD操作
  createDocument(
    title: string,
    content: string,
    folder_id: number | null, // 确保支持null值
    metadata: string,
    callback: (result: DatabaseResult<{ id: number }>) => void,
  ): void;

  getDocument(id: number | null, callback: (result: DatabaseResult<Document>) => void): void;

  updateDocument(
    id: number | null,
    title: string,
    content: string,
    folder_id: number | null, // 确保支持null值
    metadata: string,
    callback: (result: DatabaseResult<void>) => void,
  ): void;

  moveDocument(
    documentId: number | null,
    targetFolderId: number | null,
    callback: (result: DatabaseResult<void>) => void,
  ): void;

  moveFolder(
    folderId: number | null,
    targetFolderId: number | null,
    callback: (result: DatabaseResult<void>) => void,
  ): void;

  addDocumentKnowledgeAssociation(
    documentId: number | null,
    knowledgeDocumentId: number | null,
    knowledgeBaseId: number | null,
    callback: (result: {
      success: boolean;
      message: string;
      association_id?: number;
      error?: string;
    }) => void,
  ): void;

  removeDocumentKnowledgeAssociation(
    documentId: number | null,
    knowledgeBaseId: number | null,
    callback: (result: {
      success: boolean;
      message: string;
      removed_count?: number;
      error?: string;
    }) => void,
  ): void;

  clearDocumentKnowledgeAssociation(
    knowledgeDocumentId: string,
    callback: (result: {
      success: boolean;
      message: string;
      cleared_count?: number;
      error?: string;
    }) => void,
  ): void;

  clearKnowledgeBaseAssociations(
    knowledgeBaseId: number | null,
    callback: (result: {
      success: boolean;
      message: string;
      cleared_count?: number;
      error?: string;
    }) => void,
  ): void;

  updateKnowledgeDocumentAssociationTime(
    knowledgeDocumentId: number | null,
    callback: (result: {
      success: boolean;
      message: string;
      updated_count?: number;
      error?: string;
    }) => void,
  ): void;

  getDocumentKnowledgeAssociations(
    documentId: number | null,
    callback: (result: {
      success: boolean;
      associations?: Array<{
        id: number;
        knowledge_document_id: number;
        knowledge_base_id: number;
        created_at: string;
      }>;
      count?: number;
      error?: string;
    }) => void,
  ): void;

  getOriginalDocumentByKnowledgeDocumentId(
    knowledgeDocumentId: number | null,
    callback: (result: {
      success: boolean;
      id?: number;
      title?: string;
      content?: string;
      metadata?: string;
      created_at?: string;
      updated_at?: string;
      message?: string;
      error?: string;
    }) => void,
  ): void;

  isDocumentInKnowledgeBase(
    documentId: number | null,
    knowledgeBaseId: number | null,
    callback: (result: { success: boolean; exists?: boolean; error?: string }) => void,
  ): void;

  deleteDocument(id: number | null, callback: (result: DatabaseResult<void>) => void): void;

  listDocuments(
    folder_id: number | null, // 确保支持null值
    callback: (result: DatabaseResult<{ documents: Document[] }>) => void,
  ): void;

  getAllDocumentsInFolder(
    folder_id: number,
    callback: (result: DatabaseResult<{ document_ids: number[]; count: number }>) => void,
  ): void;

  // 文件夹管理
  createFolder(
    name: string,
    parent_id: number | null, // 确保支持null值
    callback: (result: DatabaseResult<{ id: number }>) => void,
  ): void;

  getFolder(id: number | null, callback: (result: DatabaseResult<Folder>) => void): void;

  updateFolder(
    id: number | null,
    name: string,
    parent_id: number | null, // 确保支持null值
    callback: (result: DatabaseResult<void>) => void,
  ): void;

  deleteFolder(id: number | null, callback: (result: DatabaseResult<void>) => void): void;

  listFolders(
    parent_id: number | null, // 确保支持null值
    callback: (result: DatabaseResult<{ folders: Folder[] }>) => void,
  ): void;

  searchFolders(keyword: string): string;

  searchDocuments(searchText: string, searchInContent: boolean, folderId: number): string;

  renameDocument(
    id: number,
    newTitle: string,
  ):
    | { success: boolean; error?: string; message?: string }
    | Promise<{ success: boolean; error?: string; message?: string }>;

  // 排序相关方法
  reorderFolders(parent_id: number, folder_ids: number[]): string;
  reorderDocuments(folder_id: number, document_ids: number[]): string;

  // 设置相关方法
  getSetting(
    key: string,
    callback: (result: { success: boolean; value?: string; error?: string }) => void,
  ): void;
  setSetting(
    key: string,
    value: string,
    callback: (result: { success: boolean; error?: string }) => void,
  ): void;
  getAppSettings(callback: (settings: AppSettings | null) => void): void;
  setAppSettings(
    settings: string,
    callback: (result: { success: boolean; error?: string }) => void,
  ): void;
  getLlmSettings(
    callback: (result: { success: boolean; settings?: LlmSettings; error?: string }) => void,
  ): void;
  setLlmSettings(
    settings: string,
    callback: (result: { success: boolean; error?: string }) => void,
  ): void;

  // 图片相关方法 - 重构版本
  saveImageFromData(
    imageData: string, // base64 字符串
    mimeType: string,
    originalUrl: string,
    callback: (result: {
      success: boolean;
      id?: number;
      file_path?: string;
      width?: number;
      height?: number;
      file_size?: number;
      is_duplicate?: boolean;
      error?: string;
    }) => void,
  ): void;
  saveImageFromFile(
    filePath: string,
    originalUrl: string,
    callback: (result: {
      success: boolean;
      id?: number;
      file_path?: string;
      width?: number;
      height?: number;
      file_size?: number;
      is_duplicate?: boolean;
      error?: string;
    }) => void,
  ): void;
  getImageAsBlob(
    image_id: number,
    callback: (result: {
      success: boolean;
      data?: string; // base64数据
      mime_type?: string;
      file_path?: string;
      error?: string;
    }) => void,
  ): void;
  addImageReference(
    image_id: number,
    document_id: number,
    callback: (result: { success: boolean; affected_rows?: number; error?: string }) => void,
  ): void;
  removeImageReference(
    image_id: number,
    document_id: number,
    callback: (result: { success: boolean; affected_rows?: number; error?: string }) => void,
  ): void;
  deleteUnreferencedImages(
    callback: (result: {
      success: boolean;
      deleted_count?: number;
      deleted_files?: string[];
      error?: string;
    }) => void,
  ): void;

  // 保持向后兼容的旧方法
  saveImage(
    document_id: number,
    imageData: string, // base64 字符串
    mimeType: string,
    callback: (result: { success: boolean; id?: number; error?: string }) => void,
  ): void;
  getImage(
    image_id: number,
    callback: (result: {
      success: boolean;
      file_url?: string;
      data?: string; // 保持向后兼容
      mime_type?: string;
      path?: string;
      full_path?: string;
      error?: string;
    }) => void,
  ): void;
  checkImageReferences(
    image_id: number,
    callback: (result: {
      success: boolean;
      hasReferences?: boolean;
      referencingDocuments?: Array<{ id: number; title: string }>;
      error?: string;
    }) => void,
  ): void;
  getImageReferences(
    image_id: number,
    callback: (result: {
      success: boolean;
      references?: Array<{ document_id: number; document_title: string }>;
      reference_count?: number;
      error?: string;
    }) => void,
  ): void;
  linkImageToDocument(
    image_id: number,
    document_id: number,
    callback: (result: { success: boolean; message?: string; error?: string }) => void,
  ): void;
  deleteImage(
    image_id: number,
    current_document_id: number,
    callback: (result: {
      success: boolean;
      error?: string;
      referencingDocuments?: Array<{ id: number; title: string }>;
    }) => void,
  ): void;
  downloadAndSaveImage(
    document_id: number,
    imageUrl: string,
    callback: (result: { success: boolean; id?: number; error?: string }) => void,
  ): void;

  // Conversation 相关方法
  createConversation(
    document_id: number | null, // 支持null值用于独立对话
    title: string,
    messages: string,
    prompt: string,
    callback: (result: DatabaseResult<{ id: number }>) => void,
  ): void;

  getConversation(
    id: number | null,
    callback: (result: DatabaseResult<Conversation>) => void,
  ): void;

  updateConversation(
    id: number | null,
    title: string,
    messages: string,
    prompt: string,
    callback: (result: DatabaseResult<void>) => void,
  ): void;

  deleteConversation(id: number | null, callback: (result: DatabaseResult<void>) => void): void;

  listConversations(
    document_id: number | null, // 支持null值用于查询独立对话
    callback: (result: DatabaseResult<{ conversations: Conversation[] }>) => void,
  ): void;

  listAllConversations(
    callback: (result: DatabaseResult<{ conversations: Conversation[] }>) => void,
  ): void;

  listChatConversations(
    callback: (result: DatabaseResult<{ conversations: Conversation[] }>) => void,
  ): void;

  // 快照相关方法
  createSnapshot(
    document_id: number,
    snapshot_name: string,
    callback: (result: {
      success: boolean;
      snapshot?: {
        name: string;
        content: string;
        created_at: string;
      };
      error?: string;
    }) => void,
  ): void;

  // 知识库相关方法
  createKnowledgeBase(
    name: string,
    description: string,
    settings: string,
    callback: (result: { success: boolean; id?: number; error?: string }) => void,
  ): void;
  getKnowledgeBase(
    id: number,
    callback: (result: KnowledgeBase & { success: boolean; error?: string }) => void,
  ): void;
  updateKnowledgeBase(
    id: number,
    name: string,
    description: string,
    settings: string,
    callback: (result: { success: boolean; error?: string }) => void,
  ): void;
  deleteKnowledgeBase(
    id: number,
    callback: (result: { success: boolean; error?: string }) => void,
  ): void;
  listKnowledgeBases(
    callback: (result: {
      success: boolean;
      knowledge_bases?: KnowledgeBase[];
      error?: string;
    }) => void,
  ): void;
  createKnowledgeDocument(
    knowledge_base_id: number,
    title: string,
    content: string,
    source_type: string,
    file_path: string,
    file_type: string,
    metadata: string,
    callback: (result: {
      success: boolean;
      knowledge_document_id?: number;
      error?: string;
    }) => void,
  ): void;
  updateKnowledgeDocument(
    knowledge_document_id: number,
    title: string,
    content: string,
    source_type: string,
    file_path: string,
    file_type: string,
    metadata: string,
    callback: (result: { success: boolean; error?: string }) => void,
  ): void;
  deleteKnowledgeDocument(
    knowledge_document_id: number,
    callback: (result: { success: boolean; error?: string }) => void,
  ): void;
  addDocumentToKnowledgeBase(
    knowledge_base_id: number,
    document_id: number,
    metadata: string,
    callback: (result: {
      success: boolean;
      knowledge_document_id?: number;
      error?: string;
    }) => void,
  ): void;
  removeDocumentFromKnowledgeBase(
    knowledge_base_id: number,
    document_id: number,
    callback: (result: { success: boolean; error?: string }) => void,
  ): void;
  listKnowledgeBaseDocuments(
    knowledge_base_id: number,
    callback: (result: {
      success: boolean;
      documents?: KnowledgeDocument[];
      error?: string;
    }) => void,
  ): void;
  createKnowledgeChunks(
    knowledge_document_id: number,
    chunks: KnowledgeChunk[],
    callback: (result: { success: boolean; chunks?: KnowledgeChunk[]; error?: string }) => void,
  ): void;
  getKnowledgeChunks(
    knowledge_document_id: number,
    callback: (result: { success: boolean; chunks?: KnowledgeChunk[]; error?: string }) => void,
  ): void;
  deleteKnowledgeChunks(
    knowledge_document_id: number,
    callback: (result: { success: boolean; deleted_count?: number; error?: string }) => void,
  ): void;
}

/**
 * @description C++ 暴露的 knowledgeApi 对象的 TypeScript 定义 (ObjectBox)
 */
export interface KnowledgeApi {
  // 知识库管理
  createKnowledgeBase(name: string, description: string, userId?: string): string;
  getAllKnowledgeBases(): string;
  getKnowledgeBase(kbId: string): string;
  updateKnowledgeBase(kbId: string, name: string, description: string): string;
  deleteKnowledgeBase(kbId: string): boolean;

  // 文档管理
  addDocumentToKnowledgeBase(
    kbId: string,
    title: string,
    content: string,
    documentType?: string,
  ): string;
  getDocumentsByKnowledgeBase(kbId: string): string;
  getDocument(docId: string): string;
  updateDocument(docId: string, title: string, content: string): boolean;
  deleteDocument(docId: string): boolean;

  // 统计
  getKnowledgeBaseStats(kbId: string): string;
  getKnowledgeBaseCount(): number;
  getDocumentCount(kbId?: string): number;

  // 测试连接
  testConnection(): string;

  // 调试方法
  debugObjectBoxData(kbId?: string): string;

  // 搜索功能
  searchKnowledgeBase(kbId: string, query: string, limit?: number): string;
  searchAllKnowledgeBases(query: string, limit?: number): string;

  // 向量功能
  generateEmbedding(text: string): string;
  updateChunkEmbeddings(docId: string): string;

  // 维护方法
  regenerateDocumentChunks(docId: string): string;

  // 新的分离式文档创建和向量化接口
  createKnowledgeDocumentOnly(
    kbId: string,
    title: string,
    content: string,
    documentType?: string,
  ): string;
  submitChunkingResults(docId: string, chunks: unknown[]): string;
  getVectorizationProgress(docId: string): string;
  checkPendingVectorization(): string;

  // 数据查看工具
  viewAllData(): string;
  viewKnowledgeBaseData(kbId: string): string;
  viewDocumentChunks(docId: string): string;
  exportKnowledgeBaseData(kbId: string): string;

  // 本地GGUF模型管理
  loadLocalGGUFModel(modelPath: string, gpuLayers?: number, contextSize?: number): boolean;
  loadLocalGGUFModelWithGpu(
    modelPath: string,
    gpuLayers?: number,
    contextSize?: number,
    selectedGpuDevice?: number,
  ): boolean;
  loadLocalGGUFModelAsync(modelPath: string, gpuLayers?: number, contextSize?: number): void;
  loadLocalGGUFModelAsyncWithGpu(
    modelPath: string,
    gpuLayers?: number,
    contextSize?: number,
    selectedGpuDevice?: number,
  ): void;
  unloadLocalGGUFModel(): void;
  isLocalGGUFLoaded(): boolean;
  getLocalGGUFInfo(): string;
  getLocalGGUFStatus(): string;
  detectGpuCapabilities(): string;
  detectAllGpuDevices(): string; // 新增：检测所有GPU设备
  getGpuDeviceInfo(deviceId: number): string; // 新增：获取特定GPU设备信息
  testLocalGGUFEmbedding(text: string): string;
  triggerAutoLoadLocalModel(): void;

  // 文件选择方法
  selectFile(extension?: string, caption?: string, filter?: string): string;

  // Qt信号 - 文档向量化完成
  documentVectorized: {
    connect: (callback: (docId: number, chunkCount: number) => void) => void;
    disconnect: (callback: (docId: number, chunkCount: number) => void) => void;
  };

  // Qt信号 - 本地模型加载完成
  localModelLoadCompleted: {
    connect: (callback: (success: boolean, message: string) => void) => void;
    disconnect: (callback: (success: boolean, message: string) => void) => void;
  };

  // Qt信号 - 向量化进度
  vectorizationProgress: {
    connect: (
      callback: (kbId: string, docId: string, completed: number, total: number) => void,
    ) => void;
    disconnect: (
      callback: (kbId: string, docId: string, completed: number, total: number) => void,
    ) => void;
  };

  // Qt信号 - 单个chunk向量化完成
  chunkVectorized: {
    connect: (
      callback: (kbId: string, docId: string, chunkIndex: number, totalChunks: number) => void,
    ) => void;
    disconnect: (
      callback: (kbId: string, docId: string, chunkIndex: number, totalChunks: number) => void,
    ) => void;
  };
}

// --- Pinia Store 类型定义 ---
export interface App {
  title?: string;
  key?: string;
  icon?: string;
  link?: string;
  type: 'app' | 'botton' | 'space';
  position: 'top' | 'bottom';
  isDev?: boolean;
  handler?: () => void;
}
export interface Perferences {
  editor: {
    fontSize: number;
    lineHeight: number;
  };
}

export interface ChatPromptBody {
  responsibilities: string[];
  emphasize: string;
}
export interface ChatPrompt {
  name: string;
  prompt: ChatPromptBody;
}
export interface AutoCompletePrompt {
  name: string;
  prompt: string;
}
export interface FloatAgentPrompt {
  name: string;
  prompt: string;
}
export interface AppSettings {
  base: {
    language: string;
    theme: 'light' | 'dark';
  };
  editor: {
    fontSize: number;
    lineHeight: number;
    enableAutoComplete: boolean;
    enableEmbeddTitle: boolean;
    enableToolbar: boolean;
    enableAutoSave: boolean;
  };
  llm: LlmSettings;
  provider: string;
  autoComplete: AutoComplete;
  floatAgent: FloatAgent;
  searchEngine: SearchEngineSettings;
  resourceProvider: ResourceProviderSettings;
  knowledgeBase: KnowledgeBaseSettings;
  prompt: {
    autoComplete: {
      selected: string;
      list: AutoCompletePrompt[];
    };
    floatAgent: {
      selected: string;
      list: FloatAgentPrompt[];
    };
    chat: {
      selected: string;
      list: ChatPrompt[];
    };
  };
}

export interface TavilySettings {
  apiKey: string;
  baseUrl: string;
  searchDepth: 'basic' | 'advanced';
  includeAnswer: boolean;
  includeRawContent: boolean;
  maxResults: number;
  includeImages: boolean;
  includeImageDescriptions: boolean;
}

export interface PexelsSettings {
  apiKey: string;
  baseUrl: string;
  maxResults: number;
}

// 搜索引擎供应商配置
export interface SearchEngineProvider {
  id: string;
  name: string;
  key: 'tavily' | 'custom';
  config: TavilySettings | Record<string, unknown>;
  description?: string;
}

// 资源服务商配置
export interface ResourceProvider {
  id: string;
  name: string;
  key: 'pexels' | 'custom';
  config: PexelsSettings | Record<string, unknown>;
  description?: string;
}

// 搜索引擎设置
export interface SearchEngineSettings {
  providers: SearchEngineProvider[];
  defaultProvider?: string;
  description?: string;
}

// 资源服务商设置
export interface ResourceProviderSettings {
  providers: ResourceProvider[];
  defaultProvider?: string;
  description?: string;
}

export interface LlmSettings {
  qwen: QwenSettings;
  ollama: OllamaSettings;
  minimax: MiniMaxSettings;
  deepseek: DeepSeekSettings;
  volces: VolcesSettings;
  moonshot: MoonshotSettings;
  anthropic: AnthropicSettings;
  openai: OpenAISettings;
  azureOpenai: AzureOpenAISettings;
  gemini: GeminiSettings;
  grok: GrokSettings;
  glm: GlmSettings;
}

export interface PexelsVideoFile {
  id: number;
  quality: string;
  file_type: string;
  width: number;
  height: number;
  fps: number;
  link: string;
}

export interface PexelsVideo {
  id: number;
  width: number;
  height: number;
  duration: number;
  full_res: string | null;
  tags: string[];
  url: string;
  image: string;
  avg_color: string;
  user: {
    id: number;
    name: string;
    url: string;
  };
  video_files: PexelsVideoFile[];
  video_pictures: Array<{
    id: number;
    picture: string;
    nr: number;
  }>;
}

/**
 * stream_options 流式输出相关选项。只有在 stream 参数为 true 时，才可设置此参数。

  include_usage
  boolean
  如果设置为 true，在流式消息最后的 data: [DONE] 之前将会传输一个额外的块。此块上的 usage 字段显示整个请求的 token 使用统计信息，而 choices 字段将始终是一个空数组。所有其他块也将包含一个 usage 字段，但其值为 null。
 */
export interface AutoComplete {
  enabled: boolean;
  base_url: string;
  api_key: string;
  body: {
    model: string; // 模型的 ID
    prompt: string; // 自动补全前缀提示词
    temperature: number;
    maxTokens: number;
    frequency_penalty: number;
    presence_penalty: number;
  };
  // 自動補全配置參數 (來自 useAutoComplete.ts 的 defaultConfig)
  autoCompleteTriggerLength: number; // 觸發自動補全的最小字符數
  autoCompleteMaxSuggestions: number; // 最大建議數量
  autoCompleteDebounceTime: number; // 防抖時間（毫秒）
}
export interface FloatAgent {
  enabled: boolean;
  base_url: string;
  api_key: string;
  body: {
    model: string; // 模型的 ID
    prompt: string; // 自动补全前缀提示词
    temperature: number;
    maxTokens: number;
    frequency_penalty: number;
    presence_penalty: number;
  };
  triggerLength: number; // 觸發自動補全的最小字符數
}
export type UiStoreType = Store<
  'ui',
  {
    leftDrawerOpen: boolean;
    rightDrawerOpen: boolean;
    isDarkTheme: boolean;
    apps: App[];
    app: string;
    perferences?: Perferences;
    settingFor?: keyof Perferences;
  },
  object,
  {
    setApp(app: string): void;
    toggleLeftDrawer(): void;
    toggleRightDrawer(): void;
    setTheme(isDark: boolean): Promise<void>;
    toggleTheme(): Promise<void>;
    loadSettings(): Promise<void>;
    saveSettings(): Promise<void>;
    watchSettings(): void;
  }
>;

// --- 全局类型声明 ---

declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: string;
      VUE_ROUTER_MODE: 'hash' | 'history' | 'abstract' | undefined;
      VUE_ROUTER_BASE: string | undefined;
    }
  }

  interface Window {
    uiStore: UiStoreType;
    isQtApp: boolean;
    EXCALIDRAW_ASSET_PATH?: string;
    QT_THEME_SETTINGS?: {
      isDarkTheme: Promise<boolean>;
    };
    qt?: {
      webChannelTransport: {
        send: (data: string) => void;
        onmessage: (payload: Record<string, unknown>) => void;
      };
    };
    QWebChannel?: new (
      transport: {
        send: (data: string) => void;
        onmessage: (payload: Record<string, unknown>) => void;
      },
      callback: (channel: {
        objects: {
          qtWindow: WindowApi;
          databaseApi: DatabaseApi;
          knowledgeApi: KnowledgeApi;
        };
      }) => void,
    ) => void;
    qtWindow: WindowApi;
    databaseApi: DatabaseApi;
    knowledgeApi: KnowledgeApi;
    performThemeSync: () => void;
    qtInitialized: Promise<void>;
  }
}

// --- 知识库相关类型定义 ---

export interface KnowledgeBase {
  id: number;
  name: string;
  description: string;
  user_id: string;
  settings: string;
  document_count: number;
  created_at: string;
  updated_at: string;
}

/**
 * 知识库文档来源类型
 */
export type DocumentSourceType =
  | 'created' // 手动创建
  | 'upload' // 文件上传
  | 'inkcop_editor' // 从编辑器添加
  | 'import' // 导入文档
  | 'template' // 模板文档
  | 'other'; // 其他来源

export interface KnowledgeDocument {
  id: number;
  knowledge_base_id: number;
  title: string;
  content: string;
  file_path?: string;
  file_type?: string;
  source_type?: DocumentSourceType; // 使用定义的类型
  metadata?: string;
  chunk_count?: number; // 已向量化的chunk数量（保持向后兼容）
  total_chunk_count?: number; // 总chunk数量
  vectorized_chunk_count?: number; // 已向量化的chunk数量
  processing?: boolean; // 添加处理状态字段
  created_at: string;
  updated_at: string;
}

export interface KnowledgeChunk {
  id?: number;
  knowledge_document_id?: number;
  chunk_index: number;
  content: string;
  embedding_id?: string;
  metadata: KnowledgeChunkMetadata;
  created_at?: string;
}

export interface KnowledgeChunkMetadata {
  knowledge_base_id?: string;
  knowledge_document_id?: string;
  document_title?: string;
  chunk_index?: number;
  chunk_content?: string;
  chunk_type?: string;
  source?: string;
  [key: string]: unknown;
}

export interface KnowledgeSearchResult {
  id: string;
  memory: string;
  score: number;
  knowledge_base_id: string;
  knowledge_document_id?: string;
  document_title?: string;
  chunk_index?: number;
  chunk_content?: string;
  metadata: KnowledgeChunkMetadata;
}

export interface KnowledgeServiceStatus {
  status: 'online' | 'offline';
}

export interface KnowledgeContextChunk {
  content: string;
  source: string;
  score: number;
  metadata: KnowledgeChunkMetadata;
}

// ==================== Agent Service Types ====================

// Agent 对话相关
export interface AgentChatMessage {
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  tool_calls?: AgentToolCall[];
  tool_call_id?: string;
  timestamp?: string;
}

export interface AgentToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface AgentChatRequest {
  message: string;
  user_id?: string;
  session_id?: string;
  context?: Record<string, unknown>;
}

export interface AgentChatResponse {
  success: boolean;
  response: string;
  iteration_count?: number;
  session_id: string;
  user_id: string;
  error?: string;
}

// Agent 历史记录
export interface AgentHistoryItem {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
}

export interface AgentHistoryResponse {
  success: boolean;
  history: AgentHistoryItem[];
  error?: string;
}

// Agent 工具相关
export interface AgentToolInfo {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, unknown>;
  };
}

export interface AgentToolsResponse {
  success: boolean;
  tools: AgentToolInfo[];
  total_tools: number;
  error?: string;
}

export interface AgentToolTestResponse {
  success: boolean;
  result: string;
  error?: string;
}

// Agent 配置和状态
export interface AgentConfigResponse {
  success: boolean;
  config: {
    service: {
      host: string;
      port: number;
      enable_cors: boolean;
      allowed_origins: string[];
    };
    llm: {
      base_url: string;
      model: string;
      temperature: number;
      max_tokens: number;
    };
    agent: {
      max_iterations: number;
      checkpointer_type: string;
      recursion_limit: number;
    };
    tools: {
      web_search: { enabled: boolean; tavily_api_key?: string };
      file_tools: { enabled: boolean; data_dir: string };
      knowledge_tools: { enabled: boolean; service_url: string };
    };
    data: {
      data_dir: string;
      agent_data_path: string;
      checkpointer_path: string;
      qdrant_path: string;
    };
  };
  error?: string;
}

export interface AgentStatsResponse {
  success: boolean;
  stats: {
    service_status: string;
    agent_model: string;
    checkpointer_type: string;
    tools_count: number;
    data_directory: string;
    total_files: number;
    total_size_mb: number;
    max_iterations: number;
    recursion_limit: number;
  };
  error?: string;
}

// Agent 会话管理
export interface AgentSession {
  user_id: string;
  session_id: string;
  created_at: string;
  last_activity: string;
  message_count: number;
}

// --- 模块声明 ---

declare module 'hevue-img-preview/v3' {
  export function previewImages(options: {
    images: Array<{ src: string; title?: string }>;
    index?: number;
  }): void;
}
