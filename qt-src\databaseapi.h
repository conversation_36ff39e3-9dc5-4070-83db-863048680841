#ifndef DATABASEAPI_H
#define DATABASEAPI_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QVariant>
#include <QJsonObject>
#include <QJsonArray>
#include <QJSEngine>
#include <QJSValue>
#include <QList>
#include <QMutex>
#include <QNetworkAccessManager>
#include <QNetworkReply>

class DatabaseApi : public QObject
{
    Q_OBJECT
public:
    explicit DatabaseApi(QObject *parent = nullptr);
    ~DatabaseApi() override;

public slots:
    // 异步回调风格
    Q_INVOKABLE void createDocument(const QString &title, const QString &content, const QVariant &folder_id, const QString &metadata, QJSValue callback);
    Q_INVOKABLE void getDocument(const QVariant &id, QJSValue callback);
    Q_INVOKABLE void updateDocument(const QVariant &id, const QString &title, const QString &content, const QVariant &folder_id, const QString &metadata, QJSValue callback);
    Q_INVOKABLE void moveDocument(const QVariant &documentId, const QVariant &targetFolderId, QJSValue callback);
    Q_INVOKABLE void moveFolder(const QVariant &folderId, const QVariant &targetFolderId, QJSValue callback);
    Q_INVOKABLE void addDocumentKnowledgeAssociation(const QVariant &documentId, const QVariant &knowledgeDocumentId, const QVariant &knowledgeBaseId, QJSValue callback);
    Q_INVOKABLE void removeDocumentKnowledgeAssociation(const QVariant &documentId, const QVariant &knowledgeBaseId, QJSValue callback);
    Q_INVOKABLE void clearDocumentKnowledgeAssociation(const QString &knowledgeDocumentId, QJSValue callback);
    Q_INVOKABLE void clearKnowledgeBaseAssociations(const QVariant &knowledgeBaseId, QJSValue callback);
    Q_INVOKABLE void updateKnowledgeDocumentAssociationTime(const QVariant &knowledgeDocumentId, QJSValue callback);
    Q_INVOKABLE void getDocumentKnowledgeAssociations(const QVariant &documentId, QJSValue callback);
    Q_INVOKABLE void getOriginalDocumentByKnowledgeDocumentId(const QVariant &knowledgeDocumentId, QJSValue callback);
    Q_INVOKABLE void isDocumentInKnowledgeBase(const QVariant &documentId, const QVariant &knowledgeBaseId, QJSValue callback);

    Q_INVOKABLE void deleteDocument(const QVariant &id, QJSValue callback);
    Q_INVOKABLE void listDocuments(const QVariant &folder_id, QJSValue callback);
    Q_INVOKABLE void getAllDocumentsInFolder(const QVariant &folder_id, QJSValue callback);

    Q_INVOKABLE void createFolder(const QString &name, const QVariant &parent_id, QJSValue callback);
    Q_INVOKABLE void getFolder(const QVariant &id, QJSValue callback);
    Q_INVOKABLE void updateFolder(const QVariant &id, const QString &name, const QVariant &parent_id, QJSValue callback);
    Q_INVOKABLE void deleteFolder(const QVariant &id, QJSValue callback);
    Q_INVOKABLE void listFolders(const QVariant &parent_id, QJSValue callback);

    Q_INVOKABLE void getSetting(const QString &key, QJSValue callback);
    Q_INVOKABLE void setSetting(const QString &key, const QString &value, QJSValue callback);

    // 新增排序相关同步方法
    Q_INVOKABLE QString reorderFolders(int parent_id, const QVariantList &folder_ids);
    Q_INVOKABLE QString reorderDocuments(int folder_id, const QVariantList &document_ids);

    // 搜索相关同步方法
    Q_INVOKABLE QString searchFolders(const QString &keyword);
    Q_INVOKABLE QString searchDocuments(const QString &searchText, bool searchInContent = false, int folderId = -1);

    // 同步方法（内部复用）
    QJsonObject createDocument(const QString &title, const QString &content, int folder_id = -1, const QString &metadata = "{}");
    QJsonObject getDocument(int id);
    QJsonObject updateDocument(int id, const QString &title, const QString &content, int folder_id = -1, const QString &metadata = "{}");
    QJsonObject moveDocument(int documentId, int targetFolderId);
    QJsonObject moveFolder(int folderId, int targetFolderId);
    QJsonObject renameDocument(int id, const QString &newTitle);
    QJsonObject addDocumentKnowledgeAssociation(int documentId, int knowledgeDocumentId, int knowledgeBaseId);
    QJsonObject removeDocumentKnowledgeAssociation(int documentId, int knowledgeBaseId);
    QJsonObject clearDocumentKnowledgeAssociation(const QString &knowledgeDocumentId);
    QJsonObject clearKnowledgeBaseAssociations(int knowledgeBaseId);
    QJsonObject updateKnowledgeDocumentAssociationTime(int knowledgeDocumentId);
    QJsonObject getDocumentKnowledgeAssociations(int documentId);
    QJsonObject getOriginalDocumentByKnowledgeDocumentId(int knowledgeDocumentId);
    QJsonObject isDocumentInKnowledgeBase(int documentId, int knowledgeBaseId);

    QJsonObject deleteDocument(int id);
    QJsonObject listDocuments(int folder_id = -1);
    QJsonObject getAllDocumentsInFolder(int folder_id);

    QJsonObject createFolder(const QString &name, int parent_id = -1);
    QJsonObject getFolder(int id);
    QJsonObject updateFolder(int id, const QString &name, int parent_id = -1);
    QJsonObject deleteFolder(int id);
    QJsonObject recursiveDeleteFolder(int folderId);
    QJsonObject listFolders(int parent_id = -1);

    QJsonObject getSetting(const QString &key);
    QJsonObject setSetting(const QString &key, const QString &value);

    // 新增排序相关同步方法
    QJsonObject updateFolderOrder(int parent_id, const QList<int> &folder_ids);
    QJsonObject updateDocumentOrder(int folder_id, const QList<int> &document_ids);

    // 设置相关方法
    QJsonObject getAppSettings();
    QJsonObject setAppSettings(const QString &settings);

    // LLM 设置相关方法
    QJsonObject getLlmSettings();
    QJsonObject setLlmSettings(const QString &settings);

    // 异步回调风格实现
    void getAppSettings(QJSValue callback);
    void setAppSettings(const QString &settings, QJSValue callback);
    void getLlmSettings(QJSValue callback);
    void setLlmSettings(const QString &settings, QJSValue callback);

    // 图片相关方法 - 重构版本
    QJsonObject saveImageFromData(const QString &imageData, const QString &mimeType, const QString &originalUrl = "");
    QJsonObject saveImageFromFile(const QString &filePath, const QString &originalUrl = "");
    QJsonObject getImageAsBlob(int imageId);
    QJsonObject addImageReference(int imageId, int documentId);
    QJsonObject removeImageReference(int imageId, int documentId);
    QJsonObject deleteUnreferencedImages();
    QJsonObject getImageReferences(int imageId);

    Q_INVOKABLE void saveImageFromData(const QString &imageData, const QString &mimeType, const QString &originalUrl, QJSValue callback);
    Q_INVOKABLE void saveImageFromFile(const QString &filePath, const QString &originalUrl, QJSValue callback);
    Q_INVOKABLE void getImageAsBlob(const QVariant &imageId, QJSValue callback);
    Q_INVOKABLE void addImageReference(const QVariant &imageId, const QVariant &documentId, QJSValue callback);
    Q_INVOKABLE void removeImageReference(const QVariant &imageId, const QVariant &documentId, QJSValue callback);
    Q_INVOKABLE void deleteUnreferencedImages(QJSValue callback);
    Q_INVOKABLE void getImageReferences(const QVariant &imageId, QJSValue callback);
    Q_INVOKABLE void linkImageToDocument(const QVariant &imageId, const QVariant &documentId, QJSValue callback);

    // 旧的图片方法 - 保持向后兼容
    QJsonObject saveImage(int document_id, const QString &imageData, const QString &mimeType);
    QJsonObject getImage(int image_id);
    QJsonObject deleteImage(int image_id, int currentDocumentId);
    QJsonObject checkImageReferences(int image_id, int excludeDocumentId = -1);
    QJsonObject downloadAndSaveImage(int document_id, const QString &imageUrl);
    QJsonObject deleteDocumentImages(int document_id);

    Q_INVOKABLE void saveImage(const QVariant &document_id, const QString &imageData, const QString &mimeType, QJSValue callback);
    Q_INVOKABLE void getImage(const QVariant &image_id, QJSValue callback);
    Q_INVOKABLE void deleteImage(const QVariant &image_id, const QVariant &current_document_id, QJSValue callback);
    Q_INVOKABLE void checkImageReferences(const QVariant &image_id, QJSValue callback);
    Q_INVOKABLE void downloadAndSaveImage(const QVariant &document_id, const QString &imageUrl, QJSValue callback);

    // Conversation 相关方法
    Q_INVOKABLE void createConversation(const QVariant &document_id, const QString &title, const QString &messages, const QString &prompt, QJSValue callback);
    Q_INVOKABLE void getConversation(const QVariant &id, QJSValue callback);
    Q_INVOKABLE void updateConversation(const QVariant &id, const QString &title, const QString &messages, const QString &prompt, QJSValue callback);
    Q_INVOKABLE void deleteConversation(const QVariant &id, QJSValue callback);
    Q_INVOKABLE void listConversations(const QVariant &document_id, QJSValue callback);
    Q_INVOKABLE void listAllConversations(QJSValue callback);
    Q_INVOKABLE void listChatConversations(QJSValue callback);

    // 同步方法（内部复用）
    QJsonObject createConversation(int document_id, const QString &title, const QString &messages, const QString &prompt);
    QJsonObject getConversation(int id);
    QJsonObject updateConversation(int id, const QString &title, const QString &messages, const QString &prompt);
    QJsonObject deleteConversation(int id);
    QJsonObject listConversations(int document_id);
    QJsonObject listAllConversations();
    QJsonObject listChatConversations();

    QJsonObject createSnapshot(int document_id, const QString &snapshot_name);
    void createSnapshot(int document_id, const QString &snapshot_name, QJSValue callback);

private:
    QSqlDatabase m_db;
    bool initDatabase();
    bool createTables();
    bool migrateDatabase(); // 添加数据库迁移方法
    void callWithJson(QJSValue callback, const QJsonObject &result);

    // 工具函数：安全地将QVariant转换为int，null值转换为-1
    int variantToIntSafe(const QVariant &variant, int defaultValue = -1);

    // 工具函数：将QVariant转换为QList<int>
    QList<int> variantToIntList(const QVariant &variant);

    // 工具函数：将QJSValue数组转换为QList<int>
    QList<int> jsValueToIntList(const QJSValue &jsValue);

    // 新增：雪花ID生成器
    quint64 generateSnowflakeId();
    static QMutex s_snowflakeMutex;
    static quint64 s_lastTimestamp;
    static quint64 s_sequence;

    // 新增：获取用户数据目录
    QString getUserDataPath();
    QString getImagesPath();
    QString getDatabasePath();

    // 新增：确保目录存在
    bool ensureDirectoryExists(const QString &path);

    // 新增：根据MIME类型获取文件扩展名
    QString getExtensionFromMimeType(const QString &mimeType);

    // 新增：中英文混合关键词拆分
    QStringList splitChineseEnglishKeywords(const QString &text);

    // 网络管理器
    QNetworkAccessManager *m_networkManager;

    // 图片下载处理
    void handleImageDownloadFinished(QNetworkReply *reply, int document_id, QJSValue callback);
};

#endif // DATABASEAPI_H