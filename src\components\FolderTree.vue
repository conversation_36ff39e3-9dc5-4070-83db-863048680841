<template>
  <div class="folder-tree-container">
    <template v-for="(folder, index) in folders" :key="folder.id">
      <q-expansion-item
        v-model="expanded[folder.id]"
        switch-toggle-side
        hide-expand-icon
        dense
        expand-icon-toggle
        :duration="100"
        class="no-padding left-switch hover-item folder-tree"
      >
        <template #header>
          <FolderItem
            :folder="folder"
            :depth="depth"
            :expanded="!!expanded[folder.id]"
            :is-creating-content="creatingContentInFolder.has(folder.id)"
            :folder-index="index"
            @toggle="toggle"
            @set-folder-ref="setFolderRef"
            @folder-updated="handleFolderUpdated"
            @folder-deleted="handleFolderDeleted"
            @subfolder-created="handleSubfolderCreated"
            @document-created="handleDocumentCreated"
            @creating-content-start="handleCreatingContentStart"
            @creating-content-end="handleCreatingContentEnd"
          />
        </template>

        <!-- 文件夹内容 -->
        <div v-if="folder.children && folder.children.length > 0" class="folder-children">
          <FolderTree
            v-if="expanded[folder.id]"
            :ref="(el) => setSubFolderRef(folder.id, el)"
            :parentId="folder.id"
            :depth="depth + 1"
          />
        </div>

        <!-- 文档列表 -->
        <template v-if="folder.documents && folder.documents.length > 0">
          <div class="documents-container">
            <template v-for="(doc, docIndex) in folder.documents" :key="doc.id">
              <DocumentItem
                :doc="doc"
                :parentId="folder.id"
                :document-index="docIndex"
                :depth="depth + 1"
                @document-updated="handleDocumentUpdated"
                @document-deleted="handleDocumentDeleted"
                @document-reorder="handleDocumentReorderEvent"
                @open="onOpenDocument(doc, folder.id)"
              >
                <template #padding>
                  <template v-if="depth + 1 > 0">
                    <div
                      v-for="i in depth + 1"
                      :key="i"
                      style="flex: 0 0 16px"
                      class="column items-center hover-show-item"
                    >
                      <div class="q-space border-left" style="width: 1px"></div>
                    </div>
                  </template>
                </template>
              </DocumentItem>
            </template>
          </div>
        </template>
      </q-expansion-item>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, onUnmounted, nextTick } from 'vue';
import { useSqlite } from '../composeables/useSqlite';
import { useDocumentActions } from '../composeables/useDocumentActions';
import DocumentItem from './DocumentItem.vue';
import FolderItem from './FolderItem.vue';

import { makeDraggable, makeDropTarget, type DragData, type DropData } from 'src/utils/dragAndDrop';
import { sortFoldersAndDocuments, SortMode } from 'src/utils/sortUtils';

import type { Folder, Document } from 'src/types/doc';
import { useUiStore } from 'src/stores/ui';
import { useDocStore } from 'src/stores/doc';

const docStore = useDocStore();
const uiStore = useUiStore();

const props = defineProps<{ parentId: number | null; depth?: number }>();

// 使用store中的文件树数据，并应用排序
const folders = computed(() => {
  let folderList: Folder[] = [];

  if (props.parentId === null) {
    // 根级别，返回store中的folderTree
    folderList = [...docStore.folderTree];
  } else {
    // 子级别，从store中查找对应文件夹的children
    const parentFolder = docStore.folderMap.get(props.parentId);
    folderList = parentFolder?.children ? [...parentFolder.children] : [];
  }

  // 应用排序
  const currentSortMode = uiStore.sortMode;
  const { sortedFolders } = sortFoldersAndDocuments(folderList, [], currentSortMode);

  // 对每个文件夹内的文档进行排序
  return sortedFolders.map((folder) => {
    const sortedFolder = { ...folder };
    if (sortedFolder.documents && sortedFolder.documents.length > 0) {
      const { sortedDocuments } = sortFoldersAndDocuments(
        [],
        sortedFolder.documents,
        currentSortMode,
      );
      sortedFolder.documents = [...sortedDocuments];
    }
    return sortedFolder;
  });
});

const expanded = ref<Record<number, boolean>>({});
const creatingContentInFolder = ref<Set<number>>(new Set());

// 拖拽状态管理
const activeDragFolder = ref<number | null>(null);
const activeDropZone = ref<string | null>(null);

// 子组件实例接口
interface SubFolderTreeInstance {
  pushSubfolder: (folder: Folder) => void;
  load: (parentId: number | null) => Promise<void>;
  refreshWithExpandedState: () => Promise<void>;
  getExpandedFolderIds: () => number[];
}

// 存储子组件引用，key是folder.id
const subFolderRefs = ref<Record<number, SubFolderTreeInstance>>({});
const folderElementRefs = ref<Record<number, HTMLElement>>({});

const { onOpenDocument } = useDocumentActions();

// 更新文件夹排序
const updateFolderOrder = async (parentId: number | null, folderIds: number[]) => {
  try {
    console.log('🔄 [FolderTree] 更新文件夹排序:', { parentId, folderIds });
    console.log(
      '🔄 [FolderTree] 排序前的文件夹列表:',
      folders.value.map((f) => ({ id: f.id, name: f.name })),
    );

    const sqliteInstance = useSqlite();
    await sqliteInstance.updateFolderOrder(parentId ?? -1, folderIds);

    console.log('🔄 [FolderTree] 数据库更新完成，前端数据已经是最新的，无需重新加载');
    // 不重新加载数据，因为前端的数据操作已经是最新的
    // await docStore.loadFolderTree(parentId);

    console.log(
      '🔄 [FolderTree] 排序后的文件夹列表:',
      folders.value.map((f) => ({ id: f.id, name: f.name })),
    );
  } catch (error) {
    console.error('❌ [FolderTree] 更新文件夹排序失败:', error);
  }
};

// 更新文档排序
const updateDocumentOrder = async (folderId: number, documentIds: number[]) => {
  try {
    console.log('🔄 [FolderTree] 更新文档排序:', { folderId, documentIds });
    const sqliteInstance = useSqlite();
    await sqliteInstance.updateDocumentOrder(folderId, documentIds);
    // 不重新加载数据，因为前端的数据操作已经是最新的
    // await docStore.loadFolderTree(props.parentId);
  } catch (error) {
    console.error('❌ [FolderTree] 更新文档排序失败:', error);
  }
};

// 设置文件夹拖拽
const setupFolderDragAndDrop = (folderElement: HTMLElement, folder: Folder, index: number) => {
  try {
    if (!folderElement || !folder || !folder.id) return;

    // 设置可拖拽
    makeDraggable(
      folderElement,
      {
        type: 'folder',
        id: folder.id,
        parentId: folder.parent_id,
        item: folder,
        index,
      },
      {
        onDragStart: () => {
          activeDragFolder.value = folder.id;
        },
        onDragEnd: () => {
          activeDragFolder.value = null;
          activeDropZone.value = null;
        },
      },
    );

    // 设置拖拽目标
    makeDropTarget(
      folderElement,
      {
        type: 'folder',
        id: folder.id,
        item: folder,
        index,
        position: 'after',
      },
      {
        onDrop: async (dragData, dropData) => {
          await handleFolderDrop(dragData, dropData);
        },
        onDragEnter: (dropData) => {
          // 检查是否是拖拽到文件夹内部
          if (dropData.position === 'inside-first') {
            console.log('🎯 [FolderTree] 拖拽进入文件夹内部，自动展开:', folder.id);
            // 只有通过自动展开计时器触发时才展开
            if (!expanded.value[folder.id]) {
              expanded.value[folder.id] = true;
            }
          }
          // 移除原有的自动展开逻辑，避免冲突
        },
      },
      {
        canDrop: (dragData, dropData) => {
          // 不能拖拽到自己或后代
          if (dragData.type === 'folder') {
            if (dragData.id === dropData.id) return false;
            return !docStore.isDescendantOf(dragData.id, dropData.id);
          }
          return true;
        },
        depth: props.depth,
        autoExpand: true,
        // 只在自定义排序模式下且拖拽的是文件夹时显示占位符
        showPlaceholder: uiStore.sortMode === SortMode.CUSTOM,
        // 检查文件夹是否已展开
        isExpanded: () => !!expanded.value[folder.id],
      },
    );
  } catch (error) {
    console.error('设置文件夹拖拽失败:', error);
  }
};

// 处理文件夹拖拽放置
const handleFolderDrop = async (dragData: DragData, dropData: DropData) => {
  console.log('🔄 [FolderTree] handleFolderDrop 被调用:', { dragData, dropData });
  console.log('🔄 [FolderTree] 当前排序模式:', uiStore.sortMode);

  if (dragData.type === 'folder') {
    // 检查是否为同级排序还是跨文件夹移动
    const draggedFolder = docStore.folderMap.get(dragData.id);
    const targetFolder = docStore.folderMap.get(dropData.id);

    if (!draggedFolder || !targetFolder) {
      console.error('❌ [FolderTree] 找不到拖拽或目标文件夹');
      return;
    }

    // 在字典排序模式下，只允许跨文件夹移动，不允许同级排序
    if (uiStore.sortMode === SortMode.ALPHABETICAL) {
      console.log('🔄 [FolderTree] 字典排序模式：只允许跨文件夹移动');
      await handleCrossFolderMove(dragData, dropData);
      return;
    }

    // 在自定义排序模式下，检查拖拽类型
    if (dropData.position === 'inside-first' || dropData.position === 'inside-last') {
      // 拖拽到文件夹内部
      console.log('🔄 [FolderTree] 自定义排序模式：拖拽到文件夹内部');
      await handleFolderToInside(dragData, dropData);
    } else if (dropData.position === 'before' || dropData.position === 'after') {
      // 检查是否是同级排序还是跨层级移动
      const draggedParentId = draggedFolder.parent_id || null;
      const targetParentId = targetFolder.parent_id || null;

      console.log('🔄 [FolderTree] 拖拽分析:', {
        draggedFolderId: dragData.id,
        draggedParentId,
        targetFolderId: dropData.id,
        targetParentId,
        currentParentId: props.parentId,
      });

      if (draggedParentId === targetParentId) {
        // 同级排序：拖拽的文件夹和目标文件夹在同一个父级下
        console.log('🔄 [FolderTree] 自定义排序模式：同级排序');
        await handleFolderReorder(dragData, dropData);
      } else {
        // 跨层级移动：需要改变父子关系并排序
        console.log('🔄 [FolderTree] 自定义排序模式：跨层级移动');
        await handleCrossLevelMove(dragData, dropData);
      }
    }
  } else {
    // 文档拖拽，直接调用跨文件夹移动
    await handleCrossFolderMove(dragData, dropData);
  }
};

// 处理文件夹同级排序
const handleFolderReorder = async (dragData: DragData, dropData: DropData) => {
  console.log('🔄 [FolderTree] 处理文件夹同级排序:', { dragData, dropData });

  try {
    // 获取当前文件夹列表
    const currentFolders = [...folders.value];
    const dragIndex = currentFolders.findIndex((f) => f.id === dragData.id);
    const dropIndex = currentFolders.findIndex((f) => f.id === dropData.id);

    if (dragIndex === -1 || dropIndex === -1) {
      console.error('❌ [FolderTree] 找不到拖拽或目标文件夹索引');
      return;
    }

    // 执行重排序
    const [draggedFolder] = currentFolders.splice(dragIndex, 1);
    let targetIndex = dropIndex;

    // 计算目标插入位置
    if (dropData.position === 'after') {
      targetIndex = dropIndex + 1;
    }

    // 如果拖拽元素原本在目标位置之前，需要调整目标索引
    // 因为移除拖拽元素后，后面的元素索引都会减1
    if (dragIndex < targetIndex) {
      targetIndex--;
    }

    // 确保索引不超出范围
    targetIndex = Math.max(0, Math.min(targetIndex, currentFolders.length));

    currentFolders.splice(targetIndex, 0, draggedFolder);

    // 更新前端对象的 sort_order 字段，确保与数组顺序一致
    currentFolders.forEach((folder, index) => {
      folder.sort_order = index;
    });

    // 更新前端数据存储中的文件夹顺序
    if (props.parentId === null) {
      // 更新根级别文件夹顺序
      docStore.folderTree.splice(0, docStore.folderTree.length, ...currentFolders);
    } else {
      // 更新子文件夹顺序
      const parentFolder = docStore.folderMap.get(props.parentId);
      if (parentFolder && parentFolder.children) {
        parentFolder.children.splice(0, parentFolder.children.length, ...currentFolders);
      }
    }

    // 更新数据库中的文件夹排序
    const folderIds = currentFolders.map((folder) => folder.id);
    await updateFolderOrder(props.parentId, folderIds);

    console.log('✅ [FolderTree] 文件夹排序更新完成');
  } catch (error) {
    console.error('❌ [FolderTree] 文件夹排序更新失败:', error);
  }
};

// 处理拖拽到文件夹内部
const handleFolderToInside = async (dragData: DragData, dropData: DropData) => {
  console.log('🔄 [FolderTree] 处理拖拽到文件夹内部:', { dragData, dropData });

  if (dragData.type === 'folder') {
    const draggedFolder = docStore.folderMap.get(dragData.id);
    const targetFolder = docStore.folderMap.get(dropData.id);

    if (!draggedFolder || !targetFolder) {
      console.error('❌ [FolderTree] 找不到拖拽或目标文件夹');
      return;
    }

    // 1. 更新父子关系：将拖拽的文件夹移动到目标文件夹内部
    const success = await docStore.moveFolderToParent(dragData.id, dropData.id);
    if (success) {
      console.log('✅ [FolderTree] 文件夹父子关系更新成功');

      // 2. 确保目标文件夹展开
      expanded.value[dropData.id] = true;

      // 3. 处理内部排序
      if (dropData.position === 'inside-first') {
        // 移动到第一个位置
        console.log('🔄 [FolderTree] 移动到文件夹内部第一个位置');
        // 这里可以添加具体的排序逻辑
      } else if (dropData.position === 'inside-last') {
        // 移动到最后一个位置
        console.log('🔄 [FolderTree] 移动到文件夹内部最后一个位置');
        // 这里可以添加具体的排序逻辑
      }
    }
  } else if (dragData.type === 'document') {
    // 文档拖拽到文件夹内部
    const success = await docStore.moveDocumentToFolder(dragData.id, dropData.id);
    if (success) {
      console.log('✅ [FolderTree] 文档移动到文件夹内部成功');
      expanded.value[dropData.id] = true;
    }
  }
};

// 跨文件夹移动
const handleCrossFolderMove = async (dragData: DragData, dropData: DropData) => {
  console.log('🔄 跨文件夹移动:', { dragData, dropData });

  if (dragData.type === 'document') {
    const currentFolderId = docStore.getDocumentFolderId(dragData.id);
    if (currentFolderId === dropData.id) return;

    const success = await docStore.moveDocumentToFolder(dragData.id, dropData.id);
    if (success) {
      console.log('✅ 文档移动成功');
    }
  } else if (dragData.type === 'folder') {
    const currentFolder = docStore.folderMap.get(dragData.id);
    if (currentFolder?.parent_id === dropData.id) return;

    const success = await docStore.moveFolderToParent(dragData.id, dropData.id);
    if (success) {
      console.log('✅ 文件夹移动成功');
      expanded.value[dropData.id] = true;
    }
  }
};

// 跨层级移动：改变父子关系并在新位置排序
const handleCrossLevelMove = async (dragData: DragData, dropData: DropData) => {
  console.log('🔄 [FolderTree] 处理跨层级移动:', { dragData, dropData });

  try {
    const draggedFolder = docStore.folderMap.get(dragData.id);
    const targetFolder = docStore.folderMap.get(dropData.id);

    if (!draggedFolder || !targetFolder) {
      console.error('❌ [FolderTree] 找不到拖拽或目标文件夹');
      return;
    }

    // 确定新的父级ID（目标文件夹的父级）
    const newParentId = targetFolder.parent_id;

    console.log('🔄 [FolderTree] 跨层级移动详情:', {
      draggedFolderId: dragData.id,
      draggedFolderName: draggedFolder.name,
      currentParentId: draggedFolder.parent_id,
      newParentId,
      targetFolderId: dropData.id,
      targetFolderName: targetFolder.name,
      position: dropData.position,
    });

    // 1. 先移动文件夹到新的父级
    const moveSuccess = await docStore.moveFolderToParent(dragData.id, newParentId);

    if (!moveSuccess) {
      console.error('❌ [FolderTree] 文件夹移动失败');
      return;
    }

    console.log('✅ [FolderTree] 文件夹父级关系更新成功');

    // 2. 然后在新位置进行排序
    // 从前端数据获取新父级下的所有文件夹，避免重新查询数据库
    let newParentFolders: Folder[] = [];

    if (newParentId === null || newParentId === -1) {
      // 根级别文件夹
      newParentFolders = docStore.folderTree;
    } else {
      // 子级文件夹
      const parentFolder = docStore.folderMap.get(newParentId);
      newParentFolders = parentFolder?.children || [];
    }

    if (newParentFolders.length === 0) {
      console.log('✅ [FolderTree] 新父级下没有其他文件夹，无需排序');
      return;
    }

    // 找到目标文件夹在新父级下的索引
    const targetIndex = newParentFolders.findIndex((f) => f.id === dropData.id);
    if (targetIndex === -1) {
      console.error('❌ [FolderTree] 在新父级下找不到目标文件夹');
      return;
    }

    // 计算插入位置
    let insertIndex = targetIndex;
    if (dropData.position === 'after') {
      insertIndex = targetIndex + 1;
    }

    // 重新排列文件夹顺序
    const reorderedFolders = [...newParentFolders];
    const draggedFolderIndex = reorderedFolders.findIndex((f) => f.id === dragData.id);

    if (draggedFolderIndex !== -1) {
      // 移除拖拽的文件夹
      const [movedFolder] = reorderedFolders.splice(draggedFolderIndex, 1);

      // 调整插入位置（如果拖拽的文件夹在目标位置之前）
      if (draggedFolderIndex < insertIndex) {
        insertIndex--;
      }

      // 插入到新位置
      reorderedFolders.splice(insertIndex, 0, movedFolder);

      // 更新前端对象的 sort_order 字段，确保与数组顺序一致
      reorderedFolders.forEach((folder, index) => {
        folder.sort_order = index;
      });

      // 更新前端数据存储中的文件夹顺序
      if (newParentId === null || newParentId === -1) {
        // 更新根级别文件夹顺序
        docStore.folderTree.splice(0, docStore.folderTree.length, ...reorderedFolders);
      } else {
        // 更新子文件夹顺序
        const parentFolder = docStore.folderMap.get(newParentId);
        if (parentFolder && parentFolder.children) {
          parentFolder.children.splice(0, parentFolder.children.length, ...reorderedFolders);
        }
      }

      // 更新排序
      const { useSqlite } = await import('src/composeables/useSqlite');
      const folderIds = reorderedFolders.map((f) => f.id);
      await useSqlite().updateFolderOrder(newParentId, folderIds);

      console.log('✅ [FolderTree] 跨层级移动和排序完成');
    }
  } catch (error) {
    console.error('❌ [FolderTree] 跨层级移动失败:', error);
  }
};

// 其他方法保持不变...
const setSubFolderRef = (folderId: number, el: unknown) => {
  if (el && typeof el === 'object' && el !== null && 'pushSubfolder' in el) {
    subFolderRefs.value[folderId] = el as SubFolderTreeInstance;
  } else {
    delete subFolderRefs.value[folderId];
  }
};

const setFolderRef = (folderId: number, el: unknown) => {
  try {
    if (!folderId || typeof folderId !== 'number' || folderId <= 0) return;

    if (el && el instanceof HTMLElement) {
      if (folderElementRefs.value[folderId] === el) return;

      folderElementRefs.value[folderId] = el;
      const folder = folders.value.find((f) => f.id === folderId);
      if (folder) {
        // 找到文件夹在当前列表中的索引
        const index = folders.value.findIndex((f) => f.id === folderId);
        setTimeout(() => {
          setupFolderDragAndDrop(el, folder, index);
        }, 0);
      }
    } else {
      delete folderElementRefs.value[folderId];
    }
  } catch (error) {
    console.error('设置文件夹引用失败:', error);
  }
};

const load = async (parentId: number | null) => {
  try {
    await docStore.loadFolderTree(parentId);
  } catch (error) {
    console.error('加载文件夹树失败:', error);
  }
};

const toggle = async (id: number) => {
  try {
    if (!id || typeof id !== 'number' || id <= 0) return;

    if (creatingContentInFolder.value.has(id) && expanded.value[id]) return;

    expanded.value[id] = !expanded.value[id];
    uiStore.highlightTreeItem = `folder-${id}`;

    // 展开文件夹时需要加载子项数据
    if (expanded.value[id]) {
      await load(id);
    }
  } catch (error) {
    console.error('切换文件夹展开状态失败:', error);
    if (id && typeof id === 'number') {
      expanded.value[id] = false;
    }
  }
};

// 事件处理方法
const handleFolderUpdated = (updatedFolder: Folder) => {
  console.log('🔄 [FolderTree] 处理文件夹更新:', {
    folderId: updatedFolder.id,
    name: updatedFolder.name,
  });

  // 确保docStore中的文件夹信息已更新
  // 注意：FolderItem中已经调用了updateFolderInTreeData，这里是双重保险
  docStore.updateFolderInTreeData(updatedFolder);

  // 强制触发响应式更新
  docStore.buildFolderMaps();
};
const handleFolderDeleted = () => {
  /* 文件夹已删除 */
};

const handleSubfolderCreated = async (folder: Folder) => {
  await nextTick();
  if (folder.parent_id !== null && folder.parent_id !== -1) {
    expanded.value[folder.parent_id] = true;
    // 不需要重新加载数据，因为 docStore.addFolderToTree 已经更新了前端数据
  }
};

const handleDocumentCreated = async (_document: Document, parentId: number) => {
  await nextTick();
  if (parentId !== null && parentId !== -1) {
    expanded.value[parentId] = true;
  }
  // 不需要重新加载数据，因为 docStore.addDocumentToTree 已经更新了前端数据
};

const handleDocumentUpdated = (updatedDocument: Document) => {
  console.log('🔄 [FolderTree] 处理文档更新:', {
    docId: updatedDocument.id,
    title: updatedDocument.title,
  });

  // 更新docStore中的文档信息
  docStore.updateDocumentInTree(updatedDocument);

  // 同步所有窗口中的文档信息
  docStore.syncDocuments(updatedDocument);
};

const handleDocumentDeleted = async (doc_id: number) => {
  for (const win of docStore.splitterWindows) {
    for (const doc of win.documents) {
      if (doc.id === doc_id) {
        docStore.removeDocument(win.id, doc_id);
      }
    }
  }
  try {
    await useSqlite().deleteDocument(doc_id);
    docStore.removeDocumentFromTree(doc_id);
  } catch (error) {
    console.error('删除文档失败:', error);
  }
};

const handleCreatingContentStart = (folderId: number) => {
  creatingContentInFolder.value.add(folderId);
  expanded.value[folderId] = true;
};

const handleCreatingContentEnd = (folderId: number) => {
  creatingContentInFolder.value.delete(folderId);
};

const handleDocumentReorderEvent = async (data: {
  dragIndex: number;
  dropIndex: number;
  position: 'before' | 'after';
  parentId: number;
  draggedDocumentId?: number; // 添加被拖拽文档的ID
  newParentId?: number; // 添加新的父文件夹ID
}) => {
  try {
    console.log('🔄 [FolderTree] 处理文档重排序:', data);

    // 根据parentId找到目标文件夹（文档要放置到的文件夹）
    const targetFolder = docStore.folderMap.get(data.parentId);

    if (!targetFolder) {
      console.warn('❌ [FolderTree] 未找到目标文件夹:', data.parentId);
      return;
    }

    // 确保目标文件夹有documents数组
    if (!targetFolder.documents) {
      targetFolder.documents = [];
    }

    const { dropIndex, position } = data;

    // 获取被拖拽的文档（通过文档ID查找，而不是index）
    let draggedDoc: Document | null = null;
    let sourceFolder: Folder | null = null;

    // 使用文档ID查找被拖拽的文档
    if (data.draggedDocumentId) {
      for (const folder of docStore.folderMap.values()) {
        if (folder.documents) {
          const doc = folder.documents.find((d) => d.id === data.draggedDocumentId);
          if (doc) {
            draggedDoc = doc;
            sourceFolder = folder;
            break;
          }
        }
      }
    } else {
      console.warn('❌ [FolderTree] 缺少 draggedDocumentId，无法准确定位被拖拽的文档');
      return;
    }

    if (!draggedDoc || !sourceFolder) {
      console.warn('❌ [FolderTree] 未找到被拖拽的文档');
      return;
    }

    console.log('🔄 [FolderTree] 找到被拖拽文档:', {
      docId: draggedDoc.id,
      docTitle: draggedDoc.title,
      sourceFolderId: sourceFolder.id,
      targetFolderId: targetFolder.id,
    });

    // 检查是否是跨文件夹移动
    const isCrossFolderMove = sourceFolder.id !== targetFolder.id;

    if (isCrossFolderMove) {
      console.log('🔄 [FolderTree] 跨文件夹移动文档');

      // 1. 先从源文件夹中移除文档（通过文档ID查找并移除）
      const sourceDocuments = [...sourceFolder.documents];
      const draggedDocIndex = sourceDocuments.findIndex((d) => d.id === draggedDoc.id);
      if (draggedDocIndex === -1) {
        console.warn('❌ [FolderTree] 在源文件夹中找不到被拖拽的文档');
        return;
      }
      sourceDocuments.splice(draggedDocIndex, 1);

      // 更新源文件夹的文档排序
      sourceDocuments.forEach((doc, index) => {
        doc.sort_order = index;
      });
      sourceFolder.documents = sourceDocuments;

      // 2. 更新文档的父文件夹
      const contentJson =
        typeof draggedDoc.content === 'string'
          ? JSON.parse(draggedDoc.content)
          : draggedDoc.content;

      await useSqlite().updateDocument(
        draggedDoc.id,
        draggedDoc.title,
        contentJson,
        targetFolder.id,
        draggedDoc.metadata,
      );

      // 3. 将文档添加到目标文件夹
      const targetDocuments = [...targetFolder.documents];
      let targetIndex = dropIndex;

      if (position === 'after') {
        targetIndex = Math.min(dropIndex + 1, targetDocuments.length);
      }

      targetDocuments.splice(targetIndex, 0, draggedDoc);

      // 更新目标文件夹的文档排序
      targetDocuments.forEach((doc, index) => {
        doc.sort_order = index;
      });
      targetFolder.documents = targetDocuments;

      // 4. 更新数据库中的排序
      const sourceDocumentIds = sourceDocuments.map((doc) => doc.id);
      const targetDocumentIds = targetDocuments.map((doc) => doc.id);

      await updateDocumentOrder(sourceFolder.id, sourceDocumentIds);
      await updateDocumentOrder(targetFolder.id, targetDocumentIds);

      console.log('✅ [FolderTree] 跨文件夹文档移动完成');
    } else {
      console.log('🔄 [FolderTree] 同文件夹内文档排序');

      // 同文件夹内的排序逻辑（通过文档ID查找并移动）
      const documents = [...targetFolder.documents];
      const draggedDocIndex = documents.findIndex((d) => d.id === draggedDoc.id);
      if (draggedDocIndex === -1) {
        console.warn('❌ [FolderTree] 在目标文件夹中找不到被拖拽的文档');
        return;
      }

      // 先计算目标插入位置（在移除被拖拽文档之前）
      let targetIndex = dropIndex;
      if (position === 'after') {
        targetIndex = Math.min(dropIndex + 1, documents.length);
      }

      console.log('🔄 [FolderTree] 排序计算:', {
        draggedDocIndex,
        dropIndex,
        position,
        originalTargetIndex: targetIndex,
        documentsBeforeRemove: documents.map((d) => d.title),
      });

      // 移除被拖拽的文档
      const [movedDoc] = documents.splice(draggedDocIndex, 1);

      // 如果被拖拽文档的原始位置在目标位置之前，需要调整目标索引
      if (draggedDocIndex < targetIndex) {
        targetIndex--;
      }

      console.log('🔄 [FolderTree] 调整后的目标索引:', {
        adjustedTargetIndex: targetIndex,
        documentsAfterRemove: documents.map((d) => d.title),
        movedDocTitle: movedDoc.title,
      });

      // 插入到目标位置
      documents.splice(targetIndex, 0, movedDoc);

      console.log('🔄 [FolderTree] 排序完成:', {
        finalDocuments: documents.map((d) => d.title),
      });

      // 更新前端对象的 sort_order 字段，确保与数组顺序一致
      documents.forEach((doc, index) => {
        doc.sort_order = index;
      });

      // 更新前端数据存储中的文档顺序
      targetFolder.documents = documents;

      // 更新数据库中的文档排序
      const documentIds = documents.map((doc) => doc.id);
      await updateDocumentOrder(targetFolder.id, documentIds);

      console.log('✅ [FolderTree] 同文件夹文档排序完成');
    }

    // 同时更新 docStore 中的文件夹数据
    const folderInStore = docStore.folderMap.get(targetFolder.id);
    if (folderInStore) {
      folderInStore.documents = targetFolder.documents;
    }

    if (isCrossFolderMove && sourceFolder) {
      const sourceFolderInStore = docStore.folderMap.get(sourceFolder.id);
      if (sourceFolderInStore) {
        sourceFolderInStore.documents = sourceFolder.documents;
      }
    }
  } catch (error) {
    console.error('❌ [FolderTree] 文档重排序失败:', error);
  }
};

// 清理
const cleanupDragAndDrop = () => {
  activeDragFolder.value = null;
  activeDropZone.value = null;
};

onMounted(() => {
  if (props.parentId === null) {
    void load(props.parentId);
  }
});

onUnmounted(() => {
  cleanupDragAndDrop();
});

watch(
  () => props.parentId,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      void load(newVal);
    }
  },
);

// 监听排序模式变化，重新设置拖拽目标
watch(
  () => uiStore.sortMode,
  () => {
    // 重新设置所有文件夹的拖拽目标
    void nextTick(() => {
      folders.value.forEach((folder, index) => {
        const folderElement = folderElementRefs.value[folder.id];
        if (folderElement) {
          setupFolderDragAndDrop(folderElement, folder, index);
        }
      });
    });
  },
);

// 监听UI store中的文件夹展开事件
watch(
  () => uiStore.expandFolderPath,
  async () => {
    if (uiStore.pendingExpansionPath.length > 0) {
      console.log('📂 [FolderTree] 收到展开事件，路径:', uiStore.pendingExpansionPath);
      await expandFolderPathLocal(uiStore.pendingExpansionPath);
      // 清除待展开路径
      uiStore.pendingExpansionPath = [];
    }
  },
);

// 展开指定路径的所有文件夹
const expandFolderPathLocal = async (folderPath: number[]) => {
  console.log('📂 [FolderTree] 开始展开文件夹路径:', folderPath);

  for (const folderId of folderPath) {
    // 检查文件夹是否已经展开
    if (!expanded.value[folderId]) {
      console.log('📂 [FolderTree] 展开文件夹:', folderId);

      // 设置展开状态
      expanded.value[folderId] = true;

      // 加载文件夹数据
      await load(folderId);

      // 等待展开动画完成
      await new Promise((resolve) => setTimeout(resolve, 150));
    } else {
      console.log('📂 [FolderTree] 文件夹已展开:', folderId);
    }
  }

  console.log('✅ [FolderTree] 文件夹路径展开完成');
};

// 保持兼容性的方法
const pushSubfolder = (newFolder: Folder) => {
  void docStore.addFolderToTree(newFolder, props.parentId);
};

// 刷新并保持展开状态
const refreshWithExpandedState = async () => {
  const expandedIds = getExpandedFolderIds();
  await load(props.parentId);
  // 恢复展开状态
  for (const id of expandedIds) {
    expanded.value[id] = true;
  }
};

// 获取当前展开的文件夹ID列表
const getExpandedFolderIds = (): number[] => {
  return Object.keys(expanded.value)
    .filter((key) => expanded.value[parseInt(key)])
    .map((key) => parseInt(key));
};

// 获取当前文件夹列表
const getFolders = () => {
  return folders.value;
};

defineExpose({
  load,
  pushSubfolder,
  setupFolderDragAndDrop,
  refreshWithExpandedState,
  getExpandedFolderIds,
  getFolders,
  handleFolderDrop,
});
</script>

<style scoped>
.folder-tree-container {
  position: relative;
}

/* 拖拽相关样式 */
.dragging {
  opacity: 0.5;
  transform: scale(0.95);
  transition: all 0.2s ease;
}

.dragging-active {
  cursor: grabbing !important;
}

.drag-over {
  background-color: rgba(25, 118, 210, 0.1);
  border: 2px dashed rgba(25, 118, 210, 0.3);
  transition: all 0.2s ease;
}

:deep(.document-drag-container) {
  transition: all 0.2s ease;
}

:deep(.document-drag-container.dragging) {
  opacity: 0.5;
  transform: scale(0.95);
}
</style>
